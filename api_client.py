"""
API 客户端模块
处理与 ComfyUI TaskMonitor API 的通信
"""
import requests
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class TaskStatus(Enum):
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"
    QUEUED = "queued"
    CONNECTING = "connecting"
    DISCONNECTED = "disconnected"

@dataclass
class TaskProgress:
    node_id: Optional[int] = None
    node_type: Optional[str] = None
    step: Optional[int] = None
    total_steps: Optional[int] = None
    text_message: Optional[str] = None

@dataclass
class WorkflowProgress:
    total_nodes: Optional[int] = None
    executed_nodes: Optional[int] = None
    last_executed_node_id: Optional[int] = None

@dataclass
class QueueInfo:
    running_count: int = 0
    pending_count: int = 0
    running: list = None
    pending: list = None
    
    def __post_init__(self):
        if self.running is None:
            self.running = []
        if self.pending is None:
            self.pending = []

@dataclass
class TaskMonitorData:
    task_id: Optional[str] = None
    status: TaskStatus = TaskStatus.IDLE
    queue: QueueInfo = None
    current_task_progress: TaskProgress = None
    workflow_progress: WorkflowProgress = None
    execution_time: Optional[float] = None
    error_info: list = None
    
    def __post_init__(self):
        if self.queue is None:
            self.queue = QueueInfo()
        if self.current_task_progress is None:
            self.current_task_progress = TaskProgress()
        if self.workflow_progress is None:
            self.workflow_progress = WorkflowProgress()
        if self.error_info is None:
            self.error_info = []

class ComfyUIApiClient:
    def __init__(self, base_url: str = "http://localhost:8188", timeout: int = 5):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.last_data = TaskMonitorData()
        self.connection_status = TaskStatus.DISCONNECTED
    
    def update_config(self, base_url: str, timeout: int):
        """更新客户端配置"""
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
    
    def get_status(self) -> TaskMonitorData:
        """获取任务监控状态"""
        try:
            self.connection_status = TaskStatus.CONNECTING
            url = f"{self.base_url}/task_monitor/status"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            self.connection_status = TaskStatus.IDLE
            
            # 解析响应数据
            task_data = self._parse_response(data)
            self.last_data = task_data
            return task_data
            
        except requests.exceptions.ConnectionError:
            self.connection_status = TaskStatus.DISCONNECTED
            return self._create_error_data("连接失败：无法连接到 ComfyUI 服务器")
        except requests.exceptions.Timeout:
            self.connection_status = TaskStatus.DISCONNECTED
            return self._create_error_data("连接超时：服务器响应时间过长")
        except requests.exceptions.HTTPError as e:
            self.connection_status = TaskStatus.DISCONNECTED
            return self._create_error_data(f"HTTP 错误：{e}")
        except json.JSONDecodeError:
            self.connection_status = TaskStatus.DISCONNECTED
            return self._create_error_data("数据解析错误：服务器返回无效的 JSON 数据")
        except Exception as e:
            self.connection_status = TaskStatus.DISCONNECTED
            return self._create_error_data(f"未知错误：{str(e)}")
    
    def _parse_response(self, data: Dict[str, Any]) -> TaskMonitorData:
        """解析 API 响应数据"""
        try:
            # 解析状态
            status_str = data.get('status', 'idle')
            try:
                status = TaskStatus(status_str)
            except ValueError:
                status = TaskStatus.IDLE
            
            # 解析队列信息
            queue_data = data.get('queue', {})
            queue = QueueInfo(
                running_count=queue_data.get('running_count', 0),
                pending_count=queue_data.get('pending_count', 0),
                running=queue_data.get('running', []),
                pending=queue_data.get('pending', [])
            )
            
            # 解析当前任务进度
            progress_data = data.get('current_task_progress', {})
            current_progress = TaskProgress(
                node_id=progress_data.get('node_id'),
                node_type=progress_data.get('node_type'),
                step=progress_data.get('step'),
                total_steps=progress_data.get('total_steps'),
                text_message=progress_data.get('text_message')
            )
            
            # 解析工作流进度
            workflow_data = data.get('workflow_progress', {})
            workflow_progress = WorkflowProgress(
                total_nodes=workflow_data.get('total_nodes'),
                executed_nodes=workflow_data.get('executed_nodes'),
                last_executed_node_id=workflow_data.get('last_executed_node_id')
            )
            
            return TaskMonitorData(
                task_id=data.get('task_id'),
                status=status,
                queue=queue,
                current_task_progress=current_progress,
                workflow_progress=workflow_progress,
                execution_time=data.get('execution_time'),
                error_info=data.get('error_info', [])
            )
            
        except Exception as e:
            return self._create_error_data(f"数据解析错误：{str(e)}")
    
    def _create_error_data(self, error_message: str) -> TaskMonitorData:
        """创建错误状态的数据"""
        return TaskMonitorData(
            status=self.connection_status,
            error_info=[error_message]
        )
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connection_status not in [TaskStatus.DISCONNECTED, TaskStatus.CONNECTING]
    
    def get_connection_status(self) -> TaskStatus:
        """获取连接状态"""
        return self.connection_status
