"""
信息显示测试程序
测试所有信息组件的显示
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from api_client import TaskMonitorData, TaskStatus, QueueInfo, TaskProgress, WorkflowProgress
from ui_components import (
    StatusIndicator, ProgressDisplay, QueueDisplay, 
    ExecutionTimeDisplay, ErrorDisplay, TaskIdDisplay
)

class InfoDisplayTest:
    def __init__(self):
        # 创建窗口
        self.root = ttk_bs.Window(
            title="信息显示测试",
            themename="darkly",
            size=(450, 600),
            resizable=(True, True)
        )
        
        # 创建测试数据
        self.create_test_data()
        
        # 创建UI
        self.setup_ui()
        
        # 开始测试
        self.start_test()
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建测试任务数据
        self.test_data = TaskMonitorData()
        self.test_data.task_id = "test_task_12345678901234567890"
        self.test_data.status = TaskStatus.RUNNING
        
        # 创建队列信息
        self.test_data.queue = QueueInfo(
            running_count=2,
            pending_count=3,
            running=[
                {
                    "prompt_id": "running_prompt_123456789",
                    "nodes_in_prompt": 15,
                    "client_id": "client_abc123456789"
                },
                {
                    "prompt_id": "running_prompt_987654321",
                    "nodes_in_prompt": 8,
                    "client_id": "client_def987654321"
                }
            ],
            pending=[
                {
                    "prompt_id": "pending_prompt_111111111",
                    "nodes_in_prompt": 12
                },
                {
                    "prompt_id": "pending_prompt_222222222",
                    "nodes_in_prompt": 6
                },
                {
                    "prompt_id": "pending_prompt_333333333",
                    "nodes_in_prompt": 20
                }
            ]
        )
        
        # 创建当前任务进度
        self.test_data.current_task_progress = TaskProgress(
            node_id=42,
            node_type="KSampler",
            step=15,
            total_steps=20,
            text_message="正在生成图像，步骤 15/20"
        )
        
        # 创建工作流进度
        self.test_data.workflow_progress = WorkflowProgress(
            total_nodes=25,
            executed_nodes=18,
            last_executed_node_id=42
        )
        
        # 设置执行时间
        self.test_data.execution_time = 125.7
        
        # 设置错误信息
        self.test_data.error_info = []
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ttk_bs.Frame(self.root, padding=10)
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk_bs.Label(
            main_frame,
            text="信息显示测试",
            font=("Microsoft YaHei UI", 14, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 10))
        
        # 状态指示器
        self.status_indicator = StatusIndicator(main_frame)
        self.status_indicator.pack(fill="x", pady=(0, 10))
        
        # 任务ID显示
        self.task_id_display = TaskIdDisplay(main_frame)
        self.task_id_display.pack(fill="x", pady=(0, 5))
        
        # 进度显示
        self.progress_display = ProgressDisplay(main_frame)
        self.progress_display.pack(fill="x", pady=(0, 10))
        
        # 队列显示
        self.queue_display = QueueDisplay(main_frame)
        self.queue_display.pack(fill="x", pady=(0, 5))
        self.queue_display.set_show_details(True)  # 显示详情
        
        # 执行时间显示
        self.time_display = ExecutionTimeDisplay(main_frame)
        self.time_display.pack(fill="x", pady=(0, 5))
        
        # 错误信息显示
        self.error_display = ErrorDisplay(main_frame)
        
        # 控制按钮
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))
        
        ttk_bs.Button(
            button_frame,
            text="更新测试数据",
            bootstyle="primary",
            command=self.update_test_data
        ).pack(side="left", padx=(0, 5))
        
        ttk_bs.Button(
            button_frame,
            text="添加错误",
            bootstyle="warning",
            command=self.add_error
        ).pack(side="left", padx=(0, 5))
        
        ttk_bs.Button(
            button_frame,
            text="清除错误",
            bootstyle="success",
            command=self.clear_error
        ).pack(side="left", padx=(0, 5))
        
        ttk_bs.Button(
            button_frame,
            text="关闭",
            bootstyle="danger",
            command=self.close_window
        ).pack(side="right")
    
    def start_test(self):
        """开始测试"""
        print("开始信息显示测试...")
        self.update_all_components()
        
        # 定期更新数据
        self.root.after(2000, self.auto_update)
    
    def update_all_components(self):
        """更新所有组件"""
        try:
            print("更新所有组件...")
            
            # 更新状态指示器
            self.status_indicator.update_status(self.test_data.status)
            print(f"状态: {self.test_data.status.value}")
            
            # 更新任务ID
            self.task_id_display.update_task_id(self.test_data.task_id)
            print(f"任务ID: {self.test_data.task_id}")
            
            # 更新进度显示
            self.progress_display.update_progress(self.test_data)
            print(f"工作流进度: {self.test_data.workflow_progress.executed_nodes}/{self.test_data.workflow_progress.total_nodes}")
            print(f"任务进度: {self.test_data.current_task_progress.step}/{self.test_data.current_task_progress.total_steps}")
            
            # 更新队列信息
            self.queue_display.update_queue(self.test_data)
            print(f"队列: 运行中 {self.test_data.queue.running_count}, 等待中 {self.test_data.queue.pending_count}")
            
            # 更新执行时间
            self.time_display.update_time(self.test_data.execution_time)
            print(f"执行时间: {self.test_data.execution_time} 秒")
            
            # 更新错误信息
            self.error_display.update_errors(self.test_data.error_info)
            if self.test_data.error_info:
                print(f"错误: {self.test_data.error_info}")
            
            print("组件更新完成")
            
        except Exception as e:
            print(f"更新组件时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def update_test_data(self):
        """更新测试数据"""
        import random
        
        # 随机更新一些数据
        self.test_data.current_task_progress.step = random.randint(1, 20)
        self.test_data.workflow_progress.executed_nodes = random.randint(10, 25)
        self.test_data.execution_time += random.uniform(1, 5)
        
        # 随机改变状态
        statuses = [TaskStatus.RUNNING, TaskStatus.COMPLETED, TaskStatus.IDLE]
        self.test_data.status = random.choice(statuses)
        
        self.update_all_components()
    
    def add_error(self):
        """添加错误信息"""
        self.test_data.error_info = ["测试错误信息", "连接超时", "节点执行失败"]
        self.update_all_components()
    
    def clear_error(self):
        """清除错误信息"""
        self.test_data.error_info = []
        self.update_all_components()
    
    def auto_update(self):
        """自动更新"""
        self.update_test_data()
        self.root.after(3000, self.auto_update)
    
    def close_window(self):
        """关闭窗口"""
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        print("信息显示测试程序启动")
        print("- 测试所有信息组件的显示")
        print("- 点击按钮测试不同功能")
        print("- 数据会自动更新")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("用户中断程序")

def main():
    """主函数"""
    try:
        app = InfoDisplayTest()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
