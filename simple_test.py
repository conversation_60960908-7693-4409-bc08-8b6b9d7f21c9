"""
简单测试脚本
"""
import sys
import os

print("Python version:", sys.version)
print("Current directory:", os.getcwd())
print("Files in directory:")
for file in os.listdir('.'):
    print(f"  {file}")

print("\nTesting imports...")

try:
    import tkinter
    print("✅ tkinter available")
except ImportError as e:
    print(f"❌ tkinter not available: {e}")

try:
    import requests
    print("✅ requests available")
except ImportError as e:
    print(f"❌ requests not available: {e}")

try:
    import ttkbootstrap
    print("✅ ttkbootstrap available")
except ImportError as e:
    print(f"❌ ttkbootstrap not available: {e}")

print("\nTest complete!")
input("Press Enter to exit...")
