@echo off
title ComfyUI TaskMonitor 桌面小部件 - 卸载程序
color 0C

echo.
echo ========================================
echo   ComfyUI TaskMonitor 桌面小部件
echo         卸载程序 v1.0
echo ========================================
echo.

echo [WARNING] 此操作将删除以下内容：
echo - 虚拟环境 (venv 文件夹)
echo - 配置文件 (config.json)
echo - 测试配置文件 (test_config.json)
echo.
echo 以下文件将保留：
echo - 源代码文件
echo - README.md 文档
echo - 安装脚本
echo.

set /p confirm="确定要卸载吗？(Y/N): "
if /i not "%confirm%"=="Y" (
    echo 卸载已取消
    pause
    exit /b 0
)

echo.
echo [INFO] 开始卸载...

REM 删除虚拟环境
if exist "venv" (
    echo [INFO] 删除虚拟环境...
    rmdir /s /q "venv"
    if exist "venv" (
        echo [ERROR] 虚拟环境删除失败，可能有进程正在使用
        echo [INFO] 请关闭所有相关程序后手动删除 venv 文件夹
    ) else (
        echo [OK] 虚拟环境已删除
    )
) else (
    echo [INFO] 虚拟环境不存在，跳过
)

REM 删除配置文件
if exist "config.json" (
    echo [INFO] 删除配置文件...
    del "config.json"
    echo [OK] 配置文件已删除
) else (
    echo [INFO] 配置文件不存在，跳过
)

REM 删除测试配置文件
if exist "test_config.json" (
    echo [INFO] 删除测试配置文件...
    del "test_config.json"
    echo [OK] 测试配置文件已删除
) else (
    echo [INFO] 测试配置文件不存在，跳过
)

REM 删除 Python 缓存
if exist "__pycache__" (
    echo [INFO] 删除 Python 缓存...
    rmdir /s /q "__pycache__"
    echo [OK] Python 缓存已删除
)

REM 删除 .pyc 文件
for %%f in (*.pyc) do (
    echo [INFO] 删除 %%f
    del "%%f"
)

echo.
echo ========================================
echo           卸载完成！
echo ========================================
echo.
echo 已删除的内容：
echo - 虚拟环境
echo - 配置文件
echo - Python 缓存文件
echo.
echo 保留的内容：
echo - 源代码文件
echo - 文档和脚本
echo.
echo 如需完全删除，请手动删除整个文件夹
echo.

pause
