"""
详细信息窗口
双击小挂件后显示的半透明详细信息窗口
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import Optional, Dict, Any

class DetailWindow:
    """详细信息窗口"""
    
    def __init__(self, parent, config_manager):
        self.parent = parent
        self.config = config_manager
        
        # 窗口状态
        self.is_visible = False
        self.detail_window = None
        
        # 数据存储
        self.current_data = {}
        
        print("详细信息窗口初始化完成")
    
    def toggle_visibility(self):
        """切换窗口显示/隐藏"""
        if self.is_visible:
            self.hide()
        else:
            self.show()
    
    def show(self):
        """显示详细信息窗口"""
        if self.detail_window is None:
            self.create_detail_window()
        
        self.detail_window.deiconify()
        self.is_visible = True
        self.update_position()
        print("显示详细信息窗口")
    
    def hide(self):
        """隐藏详细信息窗口"""
        if self.detail_window:
            self.detail_window.withdraw()
        self.is_visible = False
        print("隐藏详细信息窗口")
    
    def create_detail_window(self):
        """创建详细信息窗口"""
        # 创建顶级窗口
        self.detail_window = tk.Toplevel(self.parent)
        self.detail_window.title("ComfyUI 详细信息")
        
        # 设置窗口属性
        self.detail_window.overrideredirect(True)  # 无边框
        self.detail_window.attributes('-alpha', 0.85)  # 半透明
        self.detail_window.attributes('-topmost', True)  # 置顶
        
        # 设置窗口大小
        window_width = 350
        window_height = 400
        self.detail_window.geometry(f"{window_width}x{window_height}")
        
        # 创建主框架
        main_frame = ttk_bs.Frame(
            self.detail_window,
            padding=10,
            bootstyle="dark"
        )
        main_frame.pack(fill="both", expand=True)
        
        # 标题栏
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill="x", pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="ComfyUI 任务监控",
            font=("Microsoft YaHei UI", 12, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side="left")
        
        close_btn = ttk_bs.Button(
            title_frame,
            text="✕",
            bootstyle="danger-outline",
            width=3,
            command=self.hide
        )
        close_btn.pack(side="right")
        
        # 创建滚动区域
        self.create_scrollable_content(main_frame)
        
        # 绑定双击事件关闭窗口
        self.detail_window.bind("<Double-Button-1>", lambda e: self.hide())
        
        # 初始隐藏
        self.detail_window.withdraw()
    
    def create_scrollable_content(self, parent):
        """创建可滚动的内容区域"""
        # 创建滚动框架
        scroll_frame = ttk_bs.Frame(parent)
        scroll_frame.pack(fill="both", expand=True)
        
        # 创建滚动条
        scrollbar = ttk_bs.Scrollbar(scroll_frame)
        scrollbar.pack(side="right", fill="y")
        
        # 创建文本区域
        self.info_text = tk.Text(
            scroll_frame,
            wrap="word",
            yscrollcommand=scrollbar.set,
            bg="#2b2b2b",
            fg="white",
            font=("Microsoft YaHei UI", 9),
            relief="flat",
            borderwidth=0,
            padx=10,
            pady=10
        )
        self.info_text.pack(side="left", fill="both", expand=True)
        
        # 配置滚动条
        scrollbar.config(command=self.info_text.yview)
        
        # 配置文本标签样式
        self.info_text.tag_configure("title", font=("Microsoft YaHei UI", 10, "bold"), foreground="#4CAF50")
        self.info_text.tag_configure("label", font=("Microsoft YaHei UI", 9, "bold"), foreground="#2196F3")
        self.info_text.tag_configure("value", font=("Microsoft YaHei UI", 9), foreground="white")
        self.info_text.tag_configure("error", font=("Microsoft YaHei UI", 9), foreground="#F44336")
        self.info_text.tag_configure("success", font=("Microsoft YaHei UI", 9), foreground="#4CAF50")
        self.info_text.tag_configure("warning", font=("Microsoft YaHei UI", 9), foreground="#FF9800")
    
    def update_position(self):
        """更新窗口位置（在小挂件下方）"""
        if not self.detail_window:
            return
        
        # 获取父窗口位置
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_height = self.parent.winfo_height()
        
        # 计算详细窗口位置（在小挂件下方）
        detail_x = parent_x
        detail_y = parent_y + parent_height + 10
        
        # 确保窗口不超出屏幕边界
        screen_width = self.detail_window.winfo_screenwidth()
        screen_height = self.detail_window.winfo_screenheight()
        window_width = 350
        window_height = 400
        
        if detail_x + window_width > screen_width:
            detail_x = screen_width - window_width
        
        if detail_y + window_height > screen_height:
            detail_y = parent_y - window_height - 10  # 显示在上方
        
        self.detail_window.geometry(f"+{detail_x}+{detail_y}")
    
    def update_data(self, data: Dict[str, Any]):
        """更新显示数据"""
        self.current_data = data
        
        if self.is_visible and self.info_text:
            self.refresh_display()
    
    def refresh_display(self):
        """刷新显示内容"""
        if not self.info_text:
            return
        
        # 清空现有内容
        self.info_text.delete(1.0, tk.END)
        
        # 添加标题
        self.info_text.insert(tk.END, "ComfyUI 任务监控详细信息\n\n", "title")
        
        # 显示连接状态
        self.add_section("连接状态", [
            ("服务器地址", self.config.server.url),
            ("连接状态", self.current_data.get('status', '未知')),
            ("刷新间隔", f"{self.config.server.refresh_interval}ms")
        ])
        
        # 显示任务信息
        task_id = self.current_data.get('task_id', '无')
        if task_id and task_id != '无':
            task_id_display = task_id[:16] + "..." if len(task_id) > 16 else task_id
        else:
            task_id_display = "无"
        
        self.add_section("任务信息", [
            ("任务ID", task_id_display),
            ("执行时间", f"{self.current_data.get('execution_time', 0):.1f} 秒")
        ])
        
        # 显示工作流进度
        workflow = self.current_data.get('workflow_progress', {})
        if workflow.get('total_nodes'):
            progress_pct = (workflow.get('executed_nodes', 0) / workflow.get('total_nodes', 1)) * 100
            self.add_section("工作流进度", [
                ("总节点数", str(workflow.get('total_nodes', 0))),
                ("已执行节点", str(workflow.get('executed_nodes', 0))),
                ("完成百分比", f"{progress_pct:.1f}%"),
                ("最后执行节点", str(workflow.get('last_executed_node_id', '无')))
            ])
        
        # 显示当前任务进度
        task_progress = self.current_data.get('current_task_progress', {})
        if task_progress.get('node_type'):
            self.add_section("当前任务", [
                ("节点ID", str(task_progress.get('node_id', '无'))),
                ("节点类型", task_progress.get('node_type', '无')),
                ("当前步骤", f"{task_progress.get('step', 0)}/{task_progress.get('total_steps', 0)}"),
                ("消息", task_progress.get('text_message', '无'))
            ])
        
        # 显示队列信息
        queue = self.current_data.get('queue', {})
        if queue:
            queue_info = [
                ("运行中任务", str(queue.get('running_count', 0))),
                ("等待中任务", str(queue.get('pending_count', 0)))
            ]
            
            # 显示运行中任务详情
            running_tasks = queue.get('running', [])
            for i, task in enumerate(running_tasks[:2]):  # 最多显示2个
                prompt_id = task.get('prompt_id', 'N/A')
                if prompt_id and len(prompt_id) > 12:
                    prompt_id = prompt_id[:12] + "..."
                queue_info.append((f"运行任务{i+1}", f"ID:{prompt_id} 节点:{task.get('nodes_in_prompt', 'N/A')}"))
            
            self.add_section("队列信息", queue_info)
        
        # 显示错误信息
        errors = self.current_data.get('error_info', [])
        if errors:
            self.add_section("错误信息", [(f"错误{i+1}", error) for i, error in enumerate(errors)])
    
    def add_section(self, title: str, items: list):
        """添加一个信息段落"""
        # 添加段落标题
        self.info_text.insert(tk.END, f"{title}:\n", "label")
        
        # 添加项目
        for label, value in items:
            self.info_text.insert(tk.END, f"  {label}: ", "label")
            
            # 根据值的类型设置颜色
            if isinstance(value, str):
                if value in ['运行中', 'running', '已连接', 'connected']:
                    tag = "success"
                elif value in ['错误', 'error', '断开', 'disconnected']:
                    tag = "error"
                elif value in ['等待', 'queued', '连接中', 'connecting']:
                    tag = "warning"
                else:
                    tag = "value"
            else:
                tag = "value"
            
            self.info_text.insert(tk.END, f"{value}\n", tag)
        
        self.info_text.insert(tk.END, "\n")
    
    def cleanup(self):
        """清理资源"""
        if self.detail_window:
            self.detail_window.destroy()
            self.detail_window = None
        print("详细信息窗口已清理")
