"""
拖动功能测试程序
简化版本，专门测试拖动功能
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

class DragTestWindow:
    def __init__(self):
        # 创建窗口
        self.root = ttk_bs.Window(
            title="拖动测试",
            themename="darkly",
            size=(300, 200),
            resizable=(True, True)
        )
        
        # 设置无边框
        self.root.overrideredirect(True)
        
        # 拖动相关变量
        self.dragging = False
        self.start_x = 0
        self.start_y = 0
        
        # 创建UI
        self.setup_ui()
        
        # 绑定拖动事件
        self.setup_drag_events()
        
        # 设置窗口属性
        self.root.attributes('-topmost', True)
        self.root.attributes('-alpha', 0.9)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk_bs.Frame(self.root, padding=10, bootstyle="dark")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk_bs.Label(
            main_frame,
            text="拖动测试窗口",
            font=("Microsoft YaHei UI", 12, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 10))
        
        # 说明文字
        info_label = ttk_bs.Label(
            main_frame,
            text="在此区域按住鼠标左键拖动窗口",
            font=("Microsoft YaHei UI", 9),
            bootstyle="secondary"
        )
        info_label.pack(pady=(0, 10))
        
        # 状态显示
        self.status_label = ttk_bs.Label(
            main_frame,
            text="状态: 等待拖动",
            font=("Microsoft YaHei UI", 9),
            bootstyle="info"
        )
        self.status_label.pack(pady=(0, 10))
        
        # 关闭按钮
        close_btn = ttk_bs.Button(
            main_frame,
            text="关闭",
            bootstyle="danger",
            command=self.close_window
        )
        close_btn.pack()
        
        # 保存组件引用
        self.main_frame = main_frame
        self.title_label = title_label
        self.info_label = info_label
    
    def setup_drag_events(self):
        """设置拖动事件"""
        # 为主窗口绑定事件
        self.root.bind('<Button-1>', self.start_drag)
        self.root.bind('<B1-Motion>', self.on_drag)
        self.root.bind('<ButtonRelease-1>', self.stop_drag)
        
        # 为可拖动的组件绑定事件
        draggable_widgets = [self.main_frame, self.title_label, self.info_label, self.status_label]
        
        for widget in draggable_widgets:
            widget.bind('<Button-1>', self.start_drag)
            widget.bind('<B1-Motion>', self.on_drag)
            widget.bind('<ButtonRelease-1>', self.stop_drag)
    
    def start_drag(self, event):
        """开始拖动"""
        self.dragging = True
        self.start_x = event.x_root
        self.start_y = event.y_root
        self.status_label.config(text="状态: 拖动中...")
        print(f"开始拖动: {event.x_root}, {event.y_root}")
    
    def on_drag(self, event):
        """拖动过程中"""
        if not self.dragging:
            return
        
        # 计算新位置
        x = self.root.winfo_x() + (event.x_root - self.start_x)
        y = self.root.winfo_y() + (event.y_root - self.start_y)
        
        # 边缘吸附
        x, y = self.apply_edge_snapping(x, y)
        
        # 移动窗口
        self.root.geometry(f"+{x}+{y}")
        
        # 更新起始位置
        self.start_x = event.x_root
        self.start_y = event.y_root
        
        print(f"拖动到: {x}, {y}")
    
    def stop_drag(self, event):
        """停止拖动"""
        self.dragging = False
        self.status_label.config(text="状态: 拖动完成")
        print(f"停止拖动: {event.x_root}, {event.y_root}")
    
    def apply_edge_snapping(self, x, y):
        """应用边缘吸附"""
        snap_distance = 20
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        
        # 左边缘吸附
        if x <= snap_distance:
            x = 0
            self.status_label.config(text="状态: 吸附到左边缘")
        
        # 右边缘吸附
        elif x + window_width >= screen_width - snap_distance:
            x = screen_width - window_width
            self.status_label.config(text="状态: 吸附到右边缘")
        
        # 上边缘吸附
        if y <= snap_distance:
            y = 0
            self.status_label.config(text="状态: 吸附到上边缘")
        
        # 下边缘吸附
        elif y + window_height >= screen_height - snap_distance:
            y = screen_height - window_height
            self.status_label.config(text="状态: 吸附到下边缘")
        
        return x, y
    
    def close_window(self):
        """关闭窗口"""
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        print("拖动测试程序启动")
        print("- 在窗口的标签或空白区域按住鼠标左键拖动")
        print("- 靠近屏幕边缘时会自动吸附")
        print("- 点击关闭按钮退出")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("用户中断程序")

def main():
    """主函数"""
    try:
        app = DragTestWindow()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
