@echo off
title ComfyUI TaskMonitor 桌面小部件 - 安装程序
color 0A

echo.
echo ========================================
echo   ComfyUI TaskMonitor 桌面小部件
echo         安装程序 v1.0
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] 检测到管理员权限
) else (
    echo [WARN] 建议以管理员身份运行以获得最佳体验
)

echo.
echo [STEP 1/5] 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未找到 Python！
    echo.
    echo 请先安装 Python 3.7 或更高版本：
    echo https://www.python.org/downloads/
    echo.
    echo 安装时请确保勾选 "Add Python to PATH" 选项
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [OK] Python 版本: %PYTHON_VERSION%
)

echo.
echo [STEP 2/5] 检查 pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip 未找到，尝试安装...
    python -m ensurepip --upgrade
    if errorlevel 1 (
        echo [ERROR] pip 安装失败
        pause
        exit /b 1
    )
) else (
    echo [OK] pip 可用
)

echo.
echo [STEP 3/5] 创建虚拟环境...
if exist "venv" (
    echo [INFO] 虚拟环境已存在，跳过创建
) else (
    echo [INFO] 正在创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo [ERROR] 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo [OK] 虚拟环境创建成功
)

echo.
echo [STEP 4/5] 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo [ERROR] 虚拟环境激活失败
    pause
    exit /b 1
)

echo [INFO] 升级 pip...
python -m pip install --upgrade pip

echo [INFO] 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] 依赖包安装失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查网络连接
    echo 2. 尝试使用国内镜像源：
    echo    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    echo 3. 手动安装各个包
    pause
    exit /b 1
)

echo [OK] 依赖包安装完成

echo.
echo [STEP 5/5] 测试安装...
echo [INFO] 运行 API 测试...
python test_api.py
if errorlevel 1 (
    echo [WARN] API 测试失败，但这可能是因为 ComfyUI 服务器未运行
    echo [INFO] 这不影响应用程序的安装
)

echo.
echo ========================================
echo           安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. 双击 run.bat 启动应用程序
echo 2. 或者在命令行中运行：
echo    venv\Scripts\activate.bat
echo    python main.py
echo.
echo 注意事项：
echo - 确保 ComfyUI 服务器正在运行
echo - 确保已安装 ComfyUI-TaskMonitor 扩展
echo - 首次运行时可以通过设置配置服务器地址
echo.
echo 如有问题，请查看 README.md 文档
echo.

set /p choice="是否现在启动应用程序？(Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo [INFO] 启动应用程序...
    python main.py
) else (
    echo.
    echo 安装完成，您可以稍后通过 run.bat 启动应用程序
)

pause
