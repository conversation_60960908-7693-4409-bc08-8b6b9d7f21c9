"""
配置管理模块
处理应用程序的配置保存和加载
"""
import json
import os
from typing import Dict, Any

class Config:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_config = {
            # 服务器设置
            "server_url": "http://localhost:8188",
            "refresh_interval": 1000,  # 毫秒
            "connection_timeout": 5,   # 秒
            "retry_interval": 3000,    # 毫秒

            # 窗口设置
            "window_width": 400,
            "window_height": 300,
            "window_x": 100,
            "window_y": 100,
            "always_on_top": True,
            "transparency": 0.9,
            "snap_to_edge": True,
            "snap_distance": 20,

            # 外观设置
            "theme": "darkly",
            "font_family": "Microsoft YaHei UI",
            "font_size": 9,
            "progress_bar_color": "#007acc",
            "background_color": "#2b2b2b",
            "text_color": "#ffffff",
            "accent_color": "#007acc",

            # 动画设置
            "enable_animations": True,
            "animation_speed": 200,

            # 通知设置
            "enable_notifications": True,
            "notification_sound": True,
            "show_completion_popup": True,

            # 显示设置
            "show_task_id": True,
            "show_queue_info": True,
            "show_queue_details": True,
            "show_execution_time": True,
            "show_node_details": True,
            "compact_mode": False
        }
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                # 合并默认配置和加载的配置
                config = self.default_config.copy()
                config.update(loaded_config)
                return config
            except (json.JSONDecodeError, IOError) as e:
                print(f"配置文件加载失败: {e}")
                return self.default_config.copy()
        return self.default_config.copy()

    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except IOError as e:
            print(f"配置文件保存失败: {e}")
            return False

    def get(self, key: str, default=None):
        """获取配置值"""
        return self.config.get(key, default)

    def set(self, key: str, value: Any):
        """设置配置值"""
        self.config[key] = value

    def update(self, updates: Dict[str, Any]):
        """批量更新配置"""
        self.config.update(updates)

    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()

    def get_window_geometry(self) -> str:
        """获取窗口几何信息字符串"""
        return f"{self.get('window_width')}x{self.get('window_height')}+{self.get('window_x')}+{self.get('window_y')}"

    def set_window_geometry(self, geometry: str):
        """从几何信息字符串设置窗口位置和大小"""
        try:
            # 解析几何字符串 "400x300+100+100"
            size_part, pos_part = geometry.split('+', 1)
            width, height = map(int, size_part.split('x'))
            x, y = map(int, pos_part.split('+'))

            self.set('window_width', width)
            self.set('window_height', height)
            self.set('window_x', x)
            self.set('window_y', y)
        except ValueError:
            print(f"无效的几何字符串: {geometry}")
