"""
新版本启动脚本
启动重新设计的 ComfyUI TaskMonitor 桌面小挂件
"""
import sys
import os

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        missing_packages.append("tkinter")
        print("❌ tkinter 不可用")
    
    try:
        import ttkbootstrap
        print("✅ ttkbootstrap 可用")
    except ImportError:
        missing_packages.append("ttkbootstrap")
        print("❌ ttkbootstrap 不可用")
    
    try:
        import requests
        print("✅ requests 可用")
    except ImportError:
        missing_packages.append("requests")
        print("❌ requests 不可用")
    
    return missing_packages

def install_missing_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
    
    print(f"\n需要安装以下包: {', '.join(packages)}")
    
    try:
        import subprocess
        for package in packages:
            if package == "tkinter":
                print("tkinter 是 Python 标准库的一部分，请检查 Python 安装")
                continue
            
            print(f"正在安装 {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败: {result.stderr}")
                return False
        
        return True
    except Exception as e:
        print(f"安装过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("ComfyUI TaskMonitor 桌面小挂件 v2.0 - 启动器")
    print("=" * 60)
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 需要 Python 3.7 或更高版本")
        input("按回车键退出...")
        return False
    
    print("✅ Python 版本符合要求")
    print()
    
    # 检查依赖
    print("检查依赖包...")
    missing = check_dependencies()
    
    if missing:
        print(f"\n发现 {len(missing)} 个缺失的包")
        
        choice = input("是否自动安装缺失的包？(y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            if install_missing_packages(missing):
                print("\n✅ 所有依赖包安装完成")
            else:
                print("\n❌ 依赖包安装失败")
                print("请手动运行: pip install -r requirements.txt")
                input("按回车键退出...")
                return False
        else:
            print("请手动安装依赖包后再运行应用程序")
            input("按回车键退出...")
            return False
    else:
        print("\n✅ 所有依赖包都已安装")
    
    # 启动应用程序
    print("\n" + "=" * 60)
    print("启动应用程序...")
    print("=" * 60)
    
    try:
        from main_new import main as app_main
        app_main()
        return True
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序异常退出: {e}")
        input("按回车键退出...")
