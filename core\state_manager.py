"""
状态管理器
管理应用程序的全局状态和状态变化
"""
import time
from enum import Enum
from typing import Optional, Callable, List
from dataclasses import dataclass

class AppState(Enum):
    """应用程序状态"""
    STARTING = "starting"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"

class TaskState(Enum):
    """任务状态"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"
    QUEUED = "queued"

@dataclass
class StateChange:
    """状态变化事件"""
    old_state: TaskState
    new_state: TaskState
    timestamp: float
    data: Optional[dict] = None

class StateManager:
    """状态管理器"""
    
    def __init__(self):
        # 应用程序状态
        self.app_state = AppState.STARTING
        
        # 任务状态
        self.task_state = TaskState.IDLE
        self.last_task_state = TaskState.IDLE
        
        # 执行时间跟踪
        self.execution_start_time: Optional[float] = None
        self.execution_time: float = 0.0
        
        # 状态变化监听器
        self.state_listeners: List[Callable[[StateChange], None]] = []
        
        # 状态历史
        self.state_history: List[StateChange] = []
        self.max_history = 100
        
        # 连接重试
        self.connection_attempts = 0
        self.max_connection_attempts = 10
        self.last_connection_attempt = 0
        
        print("状态管理器初始化完成")
    
    def set_app_state(self, state: AppState):
        """设置应用程序状态"""
        if self.app_state != state:
            old_state = self.app_state
            self.app_state = state
            print(f"应用状态变化: {old_state.value} -> {state.value}")
    
    def set_task_state(self, state: TaskState, data: Optional[dict] = None):
        """设置任务状态"""
        if self.task_state != state:
            old_state = self.task_state
            self.last_task_state = old_state
            self.task_state = state
            
            # 处理执行时间
            self._handle_execution_time(old_state, state)
            
            # 创建状态变化事件
            change = StateChange(
                old_state=old_state,
                new_state=state,
                timestamp=time.time(),
                data=data
            )
            
            # 添加到历史
            self._add_to_history(change)
            
            # 通知监听器
            self._notify_listeners(change)
            
            print(f"任务状态变化: {old_state.value} -> {state.value}")
    
    def _handle_execution_time(self, old_state: TaskState, new_state: TaskState):
        """处理执行时间计算"""
        current_time = time.time()
        
        # 开始执行
        if old_state != TaskState.RUNNING and new_state == TaskState.RUNNING:
            self.execution_start_time = current_time
            self.execution_time = 0.0
            print("开始执行计时")
        
        # 结束执行
        elif old_state == TaskState.RUNNING and new_state != TaskState.RUNNING:
            if self.execution_start_time:
                self.execution_time = current_time - self.execution_start_time
                print(f"执行完成，耗时: {self.execution_time:.1f} 秒")
            self.execution_start_time = None
    
    def get_current_execution_time(self) -> float:
        """获取当前执行时间"""
        if self.execution_start_time and self.task_state == TaskState.RUNNING:
            return time.time() - self.execution_start_time
        return self.execution_time
    
    def add_state_listener(self, listener: Callable[[StateChange], None]):
        """添加状态变化监听器"""
        self.state_listeners.append(listener)
        print(f"添加状态监听器，当前监听器数量: {len(self.state_listeners)}")
    
    def remove_state_listener(self, listener: Callable[[StateChange], None]):
        """移除状态变化监听器"""
        if listener in self.state_listeners:
            self.state_listeners.remove(listener)
            print(f"移除状态监听器，当前监听器数量: {len(self.state_listeners)}")
    
    def _notify_listeners(self, change: StateChange):
        """通知所有监听器"""
        for listener in self.state_listeners:
            try:
                listener(change)
            except Exception as e:
                print(f"状态监听器执行出错: {e}")
    
    def _add_to_history(self, change: StateChange):
        """添加到状态历史"""
        self.state_history.append(change)
        
        # 限制历史记录数量
        if len(self.state_history) > self.max_history:
            self.state_history = self.state_history[-self.max_history:]
    
    def get_state_history(self, limit: int = 10) -> List[StateChange]:
        """获取状态历史"""
        return self.state_history[-limit:]
    
    def is_task_running(self) -> bool:
        """检查任务是否正在运行"""
        return self.task_state == TaskState.RUNNING
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.app_state == AppState.CONNECTED
    
    def should_retry_connection(self) -> bool:
        """检查是否应该重试连接"""
        current_time = time.time()
        
        # 检查重试间隔
        if current_time - self.last_connection_attempt < 3:  # 3秒间隔
            return False
        
        # 检查重试次数
        if self.connection_attempts >= self.max_connection_attempts:
            return False
        
        return True
    
    def record_connection_attempt(self):
        """记录连接尝试"""
        self.connection_attempts += 1
        self.last_connection_attempt = time.time()
        print(f"连接尝试 {self.connection_attempts}/{self.max_connection_attempts}")
    
    def reset_connection_attempts(self):
        """重置连接尝试计数"""
        self.connection_attempts = 0
        print("连接尝试计数已重置")
    
    def get_status_summary(self) -> dict:
        """获取状态摘要"""
        return {
            'app_state': self.app_state.value,
            'task_state': self.task_state.value,
            'execution_time': self.get_current_execution_time(),
            'is_running': self.is_task_running(),
            'is_connected': self.is_connected(),
            'connection_attempts': self.connection_attempts
        }
    
    def cleanup(self):
        """清理资源"""
        self.state_listeners.clear()
        self.state_history.clear()
        print("状态管理器已清理")

# 全局状态管理器实例
state_manager = StateManager()
