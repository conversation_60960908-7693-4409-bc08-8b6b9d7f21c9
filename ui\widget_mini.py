"""
小挂件界面
简洁的桌面小挂件，显示核心状态信息
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import Optional, Callable
import math
from ui.pet_animation import PetAnimation

class MiniWidget:
    """小挂件界面"""

    def __init__(self, parent, on_click: Optional[Callable] = None):
        self.parent = parent
        self.on_click = on_click

        # 状态数据
        self.current_status = "disconnected"
        self.progress_percentage = 0
        self.task_name = "ComfyUI"

        # 动画相关
        self.flash_count = 0
        self.flash_target = 0
        self.flash_timer = None

        # 创建界面
        self.setup_ui()

        print("小挂件界面初始化完成")

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        self.main_frame = ttk_bs.Frame(
            self.parent,
            padding=4,
            bootstyle="dark"
        )
        self.main_frame.pack(fill="both", expand=True)

        # 宠物动画区域
        pet_frame = ttk_bs.Frame(self.main_frame)
        pet_frame.pack(pady=(0, 5))

        # 创建宠物动画
        self.pet_animation = PetAnimation(pet_frame, "meizi")
        self.pet_animation.start_animation()

        # 状态和进度信息框架
        info_frame = ttk_bs.Frame(self.main_frame)
        info_frame.pack(fill="x")

        # 状态指示器
        self.status_indicator = ttk_bs.Label(
            info_frame,
            text="●",
            font=("Arial", 12),
            bootstyle="secondary",
            anchor="center"
        )
        self.status_indicator.pack()

        # 进度条
        self.progress_bar = ttk_bs.Progressbar(
            info_frame,
            length=80,
            mode="determinate",
            bootstyle="primary-striped"
        )
        self.progress_bar.pack(pady=(2, 2), fill="x")

        # 进度百分比
        self.progress_label = ttk_bs.Label(
            info_frame,
            text="0%",
            font=("Microsoft YaHei UI", 8),
            bootstyle="secondary",
            anchor="center"
        )
        self.progress_label.pack()

        # 任务名称（小字体）
        self.title_label = ttk_bs.Label(
            info_frame,
            text=self.task_name,
            font=("Microsoft YaHei UI", 7),
            bootstyle="info",
            anchor="center"
        )
        self.title_label.pack()

        # 绑定点击事件
        self.bind_click_events()

    def bind_click_events(self):
        """绑定点击事件"""
        widgets_to_bind = [
            self.main_frame,
            self.title_label,
            self.status_indicator,
            self.progress_bar,
            self.progress_label
        ]

        for widget in widgets_to_bind:
            widget.bind("<Button-1>", self._on_widget_click)

    def _on_widget_click(self, event):
        """处理小挂件点击事件"""
        if self.on_click:
            self.on_click()
        print("小挂件被点击")

    def update_status(self, status: str):
        """更新状态"""
        self.current_status = status

        # 状态颜色映射
        status_colors = {
            "idle": "secondary",
            "running": "primary",
            "completed": "success",
            "error": "danger",
            "queued": "warning",
            "connecting": "info",
            "disconnected": "dark"
        }

        # 状态文本映射
        status_texts = {
            "idle": "空闲",
            "running": "运行中",
            "completed": "已完成",
            "error": "错误",
            "queued": "排队中",
            "connecting": "连接中",
            "disconnected": "未连接"
        }

        color = status_colors.get(status, "secondary")
        text = status_texts.get(status, "未知")

        # 更新状态指示器
        self.status_indicator.config(bootstyle=color)

        # 更新标题（在连接状态下显示状态文本）
        if status in ["connecting", "disconnected", "error"]:
            self.title_label.config(text=text)
        else:
            self.title_label.config(text=self.task_name)

    def update_progress(self, percentage: float, task_info: Optional[str] = None):
        """更新进度"""
        self.progress_percentage = max(0, min(100, percentage))

        # 更新进度条
        self.progress_bar.config(value=self.progress_percentage)

        # 更新进度标签
        self.progress_label.config(text=f"{self.progress_percentage:.0f}%")

        # 更新任务信息
        if task_info:
            self.task_name = task_info
            if self.current_status not in ["connecting", "disconnected", "error"]:
                self.title_label.config(text=task_info)

    def start_flash_animation(self, count: int = 3):
        """开始闪烁动画"""
        self.flash_target = count
        self.flash_count = 0
        self._flash_step()
        print(f"开始闪烁动画，次数: {count}")

    def _flash_step(self):
        """闪烁动画步骤"""
        if self.flash_count >= self.flash_target * 2:  # 每次闪烁包含亮和暗两个状态
            return

        # 切换透明度
        if self.flash_count % 2 == 0:
            # 变亮
            self.parent.attributes('-alpha', 1.0)
        else:
            # 变暗
            self.parent.attributes('-alpha', 0.3)

        self.flash_count += 1

        # 继续下一步
        self.flash_timer = self.parent.after(200, self._flash_step)

        # 闪烁完成后恢复正常透明度
        if self.flash_count >= self.flash_target * 2:
            self.parent.after(200, self._restore_alpha)

    def _restore_alpha(self):
        """恢复正常透明度"""
        from core.config import config
        self.parent.attributes('-alpha', config.ui.transparency)
        print("闪烁动画完成")

    def stop_flash_animation(self):
        """停止闪烁动画"""
        if self.flash_timer:
            self.parent.after_cancel(self.flash_timer)
            self.flash_timer = None
        self._restore_alpha()

    def set_theme(self, theme: str):
        """设置主题"""
        # 这里可以根据主题调整颜色
        if theme == "light":
            self.main_frame.config(bootstyle="light")
        else:
            self.main_frame.config(bootstyle="dark")

    def update_display(self, data: dict):
        """更新显示数据"""
        try:
            # 更新状态
            status = data.get('status', 'disconnected')
            self.update_status(status)

            # 计算总体进度
            progress = 0
            task_info = "ComfyUI"

            # 从工作流进度计算
            workflow_progress = data.get('workflow_progress', {})
            if workflow_progress.get('total_nodes') and workflow_progress.get('executed_nodes') is not None:
                total = workflow_progress['total_nodes']
                executed = workflow_progress['executed_nodes']
                progress = (executed / total * 100) if total > 0 else 0
                task_info = f"节点 {executed}/{total}"

            # 从当前任务进度计算（如果有的话）
            task_progress = data.get('current_task_progress', {})
            if task_progress.get('total_steps') and task_progress.get('step') is not None:
                total_steps = task_progress['total_steps']
                current_step = task_progress['step']
                task_progress_pct = (current_step / total_steps * 100) if total_steps > 0 else 0

                # 如果有节点进度，结合计算
                if progress > 0:
                    # 假设当前节点占总进度的一小部分
                    node_weight = 1.0 / workflow_progress.get('total_nodes', 1)
                    progress = progress + (task_progress_pct * node_weight)
                else:
                    progress = task_progress_pct

                # 更新任务信息
                if task_progress.get('node_type'):
                    task_info = f"{task_progress['node_type']} {current_step}/{total_steps}"

            # 更新进度显示
            self.update_progress(progress, task_info)

        except Exception as e:
            print(f"更新小挂件显示时出错: {e}")

    def get_widgets_for_drag(self):
        """获取可拖动的控件列表"""
        widgets = [
            self.main_frame,
            self.title_label,
            self.status_indicator,
            self.progress_label
        ]

        # 添加宠物动画控件
        if hasattr(self, 'pet_animation') and self.pet_animation.get_pet_label():
            widgets.append(self.pet_animation.get_pet_label())

        return widgets

    def cleanup(self):
        """清理资源"""
        self.stop_flash_animation()

        # 清理宠物动画
        if hasattr(self, 'pet_animation'):
            self.pet_animation.cleanup()

        print("小挂件界面已清理")
