# ComfyUI TaskMonitor 桌面小部件

一个用于实时监控 ComfyUI 工作流程进度和状态的 Windows 桌面小部件应用程序。

## 功能特点

### 🔄 实时监控
- 实时获取 ComfyUI 任务状态和进度信息
- 显示工作流整体进度和当前任务进度
- 监控队列信息（运行中和等待中的任务）
- 显示执行时间和节点详情

### 🎨 界面特性
- **无边框设计** - 现代化的小部件外观
- **拖动功能** - 可自由拖动窗口位置
- **边缘吸附** - 靠近屏幕边缘时自动吸附
- **窗口置顶** - 可选择是否始终显示在最前面
- **透明背景** - 可调节窗口透明度
- **多主题支持** - 支持多种 UI 主题

### ⚙️ 高度可定制
- **服务器配置** - 自定义 ComfyUI 服务器地址和端口
- **刷新频率** - 可调节数据更新间隔
- **外观设置** - 自定义字体、颜色、大小等
- **显示选项** - 可选择显示或隐藏特定信息
- **通知设置** - 工作流完成时的提醒功能

### 🔔 智能通知
- 工作流完成时的桌面通知
- 可选的完成弹窗提示
- 支持通知声音

### 🛠️ 错误处理
- 优雅处理 ComfyUI 服务未启动的情况
- 显示连接状态和错误信息
- 自动重试连接机制

## 系统要求

- Windows 10/11
- Python 3.7 或更高版本
- ComfyUI 服务器（需要安装 ComfyUI-TaskMonitor 扩展）

## 安装和使用

### 方法一：使用启动脚本（推荐）

1. 下载或克隆此项目到本地
2. 双击运行 `run.bat` 文件
3. 脚本会自动创建虚拟环境、安装依赖并启动应用程序

### 方法二：手动安装

1. 克隆项目：
```bash
git clone <repository-url>
cd TaskMonitor_widget
```

2. 创建虚拟环境：
```bash
python -m venv venv
venv\Scripts\activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 启动应用程序：
```bash
python main.py
```

## 配置说明

### 服务器设置
- **服务器地址**: ComfyUI 服务器的 URL（默认：http://localhost:8188）
- **刷新间隔**: 数据更新频率，单位毫秒（默认：1000ms）
- **连接超时**: API 请求超时时间，单位秒（默认：5s）
- **重试间隔**: 连接失败后的重试间隔，单位毫秒（默认：3000ms）

### 窗口设置
- **窗口大小**: 可自定义宽度和高度
- **窗口置顶**: 是否始终显示在最前面
- **透明度**: 窗口透明度（0.1-1.0）
- **边缘吸附**: 是否启用边缘自动吸附
- **吸附距离**: 触发吸附的距离，单位像素

### 外观设置
- **主题**: 支持多种 Bootstrap 主题
- **字体**: 可自定义字体族和大小
- **紧凑模式**: 更紧凑的界面布局
- **动画效果**: 是否启用界面动画

### 通知设置
- **启用通知**: 是否显示桌面通知
- **通知声音**: 是否播放通知声音
- **完成弹窗**: 是否显示完成提示弹窗
- **显示选项**: 可选择显示的信息类型

## API 接口

应用程序通过调用 ComfyUI-TaskMonitor 扩展提供的 API 接口获取数据：

- **接口地址**: `GET /task_monitor/status`
- **返回格式**: JSON
- **包含信息**:
  - 任务 ID 和状态
  - 队列信息（运行中和等待中的任务）
  - 当前任务进度（节点 ID、类型、步骤等）
  - 工作流进度（总节点数、已执行节点数等）
  - 执行时间和错误信息

## 文件结构

```
TaskMonitor_widget/
├── main.py              # 主应用程序入口
├── config.py            # 配置管理模块
├── api_client.py        # API 客户端模块
├── ui_components.py     # UI 组件模块
├── window_manager.py    # 窗口管理模块
├── settings_window.py   # 设置窗口模块
├── requirements.txt     # 依赖包列表
├── run.bat             # Windows 启动脚本
├── README.md           # 说明文档
└── config.json         # 配置文件（运行后自动生成）
```

## 故障排除

### 常见问题

1. **无法连接到 ComfyUI 服务器**
   - 确保 ComfyUI 服务器正在运行
   - 检查服务器地址和端口是否正确
   - 确认已安装 ComfyUI-TaskMonitor 扩展

2. **应用程序无法启动**
   - 检查 Python 版本是否符合要求
   - 确认所有依赖包已正确安装
   - 查看控制台错误信息

3. **界面显示异常**
   - 尝试重置配置为默认值
   - 检查主题设置是否正确
   - 重启应用程序

### 日志和调试

应用程序会在控制台输出运行日志，包括：
- 连接状态信息
- API 请求结果
- 错误信息和异常

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 许可证

本项目采用 MIT 许可证。

## 致谢

- [ComfyUI](https://github.com/comfyanonymous/ComfyUI) - 强大的 AI 图像生成工具
- [ComfyUI-TaskMonitor](https://github.com/hmwl/ComfyUI-TaskMonitor) - 任务监控扩展
- [ttkbootstrap](https://github.com/israel-dryer/ttkbootstrap) - 现代化的 Tkinter 主题
- [plyer](https://github.com/kivy/plyer) - 跨平台通知库
