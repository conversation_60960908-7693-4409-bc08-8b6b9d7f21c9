"""
ComfyUI TaskMonitor 桌面小部件 - 修复版本
主应用程序入口
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
import time
from typing import Optional

from config import Config
from api_client import ComfyUIApiClient, TaskMonitorData, TaskStatus
from ui_components import (
    StatusIndicator, ProgressDisplay, QueueDisplay,
    ExecutionTimeDisplay, ErrorDisplay, ControlPanel, TaskIdDisplay
)
from settings_window import SettingsWindow

try:
    from plyer import notification
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False

class TaskMonitorWidget:
    def __init__(self):
        # 初始化配置
        self.config = Config()

        # 初始化 API 客户端
        self.api_client = ComfyUIApiClient(
            base_url=self.config.get('server_url'),
            timeout=self.config.get('connection_timeout')
        )

        # 状态变量
        self.last_status = TaskStatus.DISCONNECTED
        self.update_thread = None
        self.running = True

        # 执行时间计算
        self.execution_start_time = None
        self.local_execution_time = 0.0

        # 创建主窗口
        self.setup_window()

        # 创建界面
        self.setup_ui()

        # 设置拖动功能
        self.setup_drag_functionality()

        # 启动数据更新线程
        self.start_update_thread()

    def setup_window(self):
        """设置主窗口"""
        self.root = ttk_bs.Window(
            title="ComfyUI TaskMonitor",
            themename=self.config.get('theme', 'darkly'),
            size=(
                self.config.get('window_width', 400),
                self.config.get('window_height', 300)
            ),
            resizable=(True, True),
            minsize=(300, 200)
        )

        # 设置无边框
        self.root.overrideredirect(True)

        # 设置窗口属性
        self.root.attributes('-topmost', self.config.get('always_on_top', True))
        self.root.attributes('-alpha', self.config.get('transparency', 0.9))

        # 设置窗口位置
        geometry = self.config.get_window_geometry()
        self.root.geometry(geometry)

    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ttk_bs.Frame(self.root, padding=10)
        main_frame.pack(fill="both", expand=True)

        # 标题栏（用于拖动）
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill="x", pady=(0, 10))

        title_label = ttk_bs.Label(
            title_frame,
            text="ComfyUI 任务监控",
            font=("Microsoft YaHei UI", 11, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side="left")

        # 控制面板
        self.control_panel = ControlPanel(
            title_frame,
            on_settings=self.show_settings,
            on_close=self.close_application
        )
        self.control_panel.pack(side="right")

        # 状态指示器
        self.status_indicator = StatusIndicator(main_frame)
        self.status_indicator.pack(fill="x", pady=(0, 10))

        # 任务ID显示
        if self.config.get('show_task_id', True):
            self.task_id_display = TaskIdDisplay(main_frame)
            self.task_id_display.pack(fill="x", pady=(0, 5))
        else:
            self.task_id_display = None

        # 进度显示
        self.progress_display = ProgressDisplay(main_frame)
        self.progress_display.pack(fill="x", pady=(0, 10))

        # 队列显示
        if self.config.get('show_queue_info', True):
            self.queue_display = QueueDisplay(main_frame)
            self.queue_display.pack(fill="x", pady=(0, 5))
            # 设置是否显示队列详情
            self.queue_display.set_show_details(self.config.get('show_queue_details', True))
        else:
            self.queue_display = None

        # 执行时间显示
        if self.config.get('show_execution_time', True):
            self.time_display = ExecutionTimeDisplay(main_frame)
            self.time_display.pack(fill="x", pady=(0, 5))
        else:
            self.time_display = None

        # 错误信息显示
        self.error_display = ErrorDisplay(main_frame)
        # 错误显示默认隐藏，有错误时才显示

        # 保存可拖动组件的引用
        self.draggable_widgets = [main_frame, title_frame, title_label, self.status_indicator]
        if self.task_id_display:
            self.draggable_widgets.append(self.task_id_display)
        self.draggable_widgets.extend([self.progress_display, self.error_display])
        if self.queue_display:
            self.draggable_widgets.append(self.queue_display)
        if self.time_display:
            self.draggable_widgets.append(self.time_display)

    def setup_drag_functionality(self):
        """设置拖动功能"""
        self.dragging = False
        self.start_x = 0
        self.start_y = 0

        # 延迟绑定拖动事件，确保UI完全创建
        self.root.after(500, self.bind_drag_events_delayed)
        self.root.after(200, self.adjust_window_size)

    def bind_drag_events_delayed(self):
        """延迟绑定拖动事件"""
        print("开始绑定拖动事件...")

        # 为主窗口绑定拖动事件
        self.root.bind('<Button-1>', self.start_drag)
        self.root.bind('<B1-Motion>', self.on_drag)
        self.root.bind('<ButtonRelease-1>', self.stop_drag)

        # 为特定的可拖动组件绑定事件
        draggable_components = []

        # 收集所有可拖动的组件
        if hasattr(self, 'status_indicator'):
            draggable_components.append(self.status_indicator)
        if hasattr(self, 'task_id_display') and self.task_id_display:
            draggable_components.append(self.task_id_display)
        if hasattr(self, 'progress_display'):
            draggable_components.append(self.progress_display)
        if hasattr(self, 'queue_display') and self.queue_display:
            draggable_components.append(self.queue_display)
        if hasattr(self, 'time_display') and self.time_display:
            draggable_components.append(self.time_display)
        if hasattr(self, 'error_display'):
            draggable_components.append(self.error_display)

        # 为每个组件及其子组件绑定拖动事件
        for component in draggable_components:
            self.bind_component_drag_events(component)

        print(f"已为 {len(draggable_components)} 个组件绑定拖动事件")

    def bind_component_drag_events(self, component):
        """为组件及其子组件绑定拖动事件"""
        try:
            # 为组件本身绑定事件
            component.bind('<Button-1>', self.start_drag)
            component.bind('<B1-Motion>', self.on_drag)
            component.bind('<ButtonRelease-1>', self.stop_drag)

            # 为子组件绑定事件
            for child in component.winfo_children():
                widget_class = child.winfo_class()
                # 排除按钮等不应响应拖动的控件
                if widget_class not in ['Button']:
                    child.bind('<Button-1>', self.start_drag)
                    child.bind('<B1-Motion>', self.on_drag)
                    child.bind('<ButtonRelease-1>', self.stop_drag)

                    # 递归处理子组件
                    self.bind_component_drag_events(child)

        except Exception as e:
            print(f"绑定组件拖动事件时出错: {e}")

    def bind_drag_to_widgets(self):
        """为可拖动的控件绑定拖动事件"""
        try:
            # 递归绑定所有子控件，但排除按钮
            self._bind_drag_recursive(self.root)
        except Exception as e:
            print(f"绑定拖动事件时出错: {e}")

    def _bind_drag_recursive(self, widget):
        """递归绑定拖动事件到所有子控件"""
        try:
            # 获取控件类型
            widget_class = widget.winfo_class()

            # 排除不应该响应拖动的控件
            if widget_class not in ['Button', 'Entry', 'Text', 'Listbox', 'Scrollbar', 'Scale', 'Combobox', 'Spinbox']:
                # 绑定拖动事件，使用 add='+' 避免覆盖现有事件
                widget.bind('<Button-1>', self.start_drag, add='+')
                widget.bind('<B1-Motion>', self.on_drag, add='+')
                widget.bind('<ButtonRelease-1>', self.stop_drag, add='+')

            # 递归处理子控件
            for child in widget.winfo_children():
                self._bind_drag_recursive(child)

        except Exception as e:
            # 忽略已销毁的控件
            pass

    def adjust_window_size(self):
        """自动调整窗口大小以适应内容"""
        try:
            # 更新所有组件的布局
            self.root.update_idletasks()

            # 获取所有组件的实际需要高度
            total_height = 0
            for child in self.root.winfo_children():
                total_height += child.winfo_reqheight()

            # 添加一些边距
            total_height += 40  # 额外边距

            # 获取当前窗口大小
            current_width = self.root.winfo_width()
            current_height = self.root.winfo_height()

            # 如果需要的高度大于当前高度，调整窗口大小
            if total_height > current_height:
                new_height = min(total_height, 600)  # 最大高度限制为600
                self.root.geometry(f"{current_width}x{new_height}")
                print(f"调整窗口大小: {current_width}x{new_height}")

                # 更新配置
                self.config.set('window_height', new_height)

        except Exception as e:
            print(f"调整窗口大小时出错: {e}")

    def start_drag(self, event):
        """开始拖动"""
        self.dragging = True
        self.start_x = event.x_root
        self.start_y = event.y_root
        print(f"开始拖动: {event.x_root}, {event.y_root}")

    def on_drag(self, event):
        """拖动过程中"""
        if not self.dragging:
            return

        # 计算新位置
        x = self.root.winfo_x() + (event.x_root - self.start_x)
        y = self.root.winfo_y() + (event.y_root - self.start_y)

        # 如果启用了边缘吸附，检查是否需要吸附
        if self.config.get('snap_to_edge', True):
            x, y = self.apply_edge_snapping(x, y)

        # 移动窗口
        self.root.geometry(f"+{x}+{y}")

        # 更新起始位置
        self.start_x = event.x_root
        self.start_y = event.y_root

    def stop_drag(self, event):
        """停止拖动"""
        self.dragging = False
        # 保存窗口位置到配置
        self.save_window_position()
        print(f"停止拖动: {event.x_root}, {event.y_root}")

    def apply_edge_snapping(self, x, y):
        """应用边缘吸附"""
        snap_distance = self.config.get('snap_distance', 15)  # 减少吸附距离
        release_distance = snap_distance + 10  # 增加释放距离
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()

        # 获取当前窗口位置
        current_x = self.root.winfo_x()
        current_y = self.root.winfo_y()

        # 左边缘吸附
        if x <= snap_distance and current_x != 0:
            x = 0
        elif current_x == 0 and x < release_distance:
            x = 0  # 保持吸附直到拖动足够远

        # 右边缘吸附
        elif x + window_width >= screen_width - snap_distance and current_x != screen_width - window_width:
            x = screen_width - window_width
        elif current_x == screen_width - window_width and x + window_width > screen_width - release_distance:
            x = screen_width - window_width

        # 上边缘吸附
        if y <= snap_distance and current_y != 0:
            y = 0
        elif current_y == 0 and y < release_distance:
            y = 0

        # 下边缘吸附
        elif y + window_height >= screen_height - snap_distance and current_y != screen_height - window_height:
            y = screen_height - window_height
        elif current_y == screen_height - window_height and y + window_height > screen_height - release_distance:
            y = screen_height - window_height

        return x, y

    def save_window_position(self):
        """保存窗口位置到配置"""
        geometry = self.root.geometry()
        self.config.set_window_geometry(geometry)

    def start_update_thread(self):
        """启动数据更新线程"""
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

    def update_loop(self):
        """数据更新循环"""
        while self.running:
            try:
                # 获取数据
                data = self.api_client.get_status()

                # 在主线程中更新 UI
                self.root.after(0, self.update_ui, data)

                # 检查状态变化
                if data.status != self.last_status:
                    self.root.after(0, self.handle_status_change, self.last_status, data.status)
                    self.last_status = data.status

                # 等待下次更新
                interval = self.config.get('refresh_interval', 1000) / 1000.0
                time.sleep(interval)

            except Exception as e:
                print(f"更新循环错误: {e}")
                time.sleep(1)

    def update_ui(self, data: TaskMonitorData):
        """更新用户界面"""
        try:
            # 更新状态指示器
            self.status_indicator.update_status(data.status)

            # 更新任务ID
            if self.task_id_display is not None:
                self.task_id_display.update_task_id(data.task_id)

            # 更新进度显示
            self.progress_display.update_progress(data)

            # 更新队列信息
            if self.queue_display is not None:
                self.queue_display.update_queue(data)

            # 更新执行时间
            if self.time_display is not None:
                # 使用服务器提供的时间，如果没有则使用本地计时
                execution_time = data.execution_time
                if execution_time is None and self.execution_start_time is not None:
                    # 正在执行中，计算当前执行时间
                    import time
                    execution_time = time.time() - self.execution_start_time
                elif execution_time is None and self.local_execution_time > 0:
                    # 使用最后记录的执行时间
                    execution_time = self.local_execution_time

                self.time_display.update_time(execution_time)

            # 更新错误信息
            self.error_display.update_errors(data.error_info)

        except Exception as e:
            print(f"UI 更新错误: {e}")
            import traceback
            traceback.print_exc()

    def handle_status_change(self, old_status: TaskStatus, new_status: TaskStatus):
        """处理状态变化"""
        import time

        # 处理执行时间计算
        if old_status != TaskStatus.RUNNING and new_status == TaskStatus.RUNNING:
            # 开始执行，记录开始时间
            self.execution_start_time = time.time()
            self.local_execution_time = 0.0
            print("开始执行，启动计时")
        elif old_status == TaskStatus.RUNNING and new_status != TaskStatus.RUNNING:
            # 执行结束，停止计时
            if self.execution_start_time:
                self.local_execution_time = time.time() - self.execution_start_time
                print(f"执行结束，总时间: {self.local_execution_time:.1f} 秒")
            self.execution_start_time = None

        # 工作流完成通知
        if (old_status == TaskStatus.RUNNING and
            new_status == TaskStatus.COMPLETED and
            self.config.get('enable_notifications', True)):
            self.show_completion_notification()

    def show_completion_notification(self):
        """显示完成通知"""
        if NOTIFICATIONS_AVAILABLE:
            try:
                notification.notify(
                    title="ComfyUI TaskMonitor",
                    message="工作流执行完成！",
                    timeout=5
                )
            except Exception as e:
                print(f"通知显示失败: {e}")

        # 如果启用了完成弹窗
        if self.config.get('show_completion_popup', True):
            self.show_completion_popup()

    def show_completion_popup(self):
        """显示完成弹窗"""
        popup = ttk_bs.Toplevel(self.root)
        popup.title("任务完成")
        popup.geometry("300x150")
        popup.resizable(False, False)
        popup.transient(self.root)
        popup.grab_set()

        # 居中显示
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() - popup.winfo_width()) // 2
        y = (popup.winfo_screenheight() - popup.winfo_height()) // 2
        popup.geometry(f"+{x}+{y}")

        # 内容
        frame = ttk_bs.Frame(popup, padding=20)
        frame.pack(fill="both", expand=True)

        ttk_bs.Label(
            frame,
            text="🎉 工作流执行完成！",
            font=("Microsoft YaHei UI", 12, "bold"),
            bootstyle="success"
        ).pack(pady=(0, 20))

        ttk_bs.Button(
            frame,
            text="确定",
            bootstyle="primary",
            command=popup.destroy
        ).pack()

        # 3秒后自动关闭
        popup.after(3000, popup.destroy)

    def show_settings(self):
        """显示设置窗口"""
        SettingsWindow(self.root, self.config, self.apply_settings)

    def apply_settings(self):
        """应用设置更改"""
        try:
            # 更新 API 客户端配置
            self.api_client.update_config(
                self.config.get('server_url'),
                self.config.get('connection_timeout')
            )

            # 更新窗口属性
            self.root.attributes('-topmost', self.config.get('always_on_top'))
            self.root.attributes('-alpha', self.config.get('transparency'))

        except Exception as e:
            print(f"应用设置时出错: {e}")

    def close_application(self):
        """关闭应用程序"""
        self.running = False

        # 保存配置
        self.save_window_position()
        self.config.save_config()

        # 关闭窗口
        self.root.quit()
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.close_application)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.close_application()

def main():
    """主函数"""
    try:
        app = TaskMonitorWidget()
        app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
