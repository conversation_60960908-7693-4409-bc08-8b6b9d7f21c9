"""
宠物动画组件
处理宠物图片的动画显示
"""
import tkinter as tk
import os
from PIL import Image, ImageTk
from typing import List, Optional

class PetAnimation:
    """宠物动画管理器"""

    def __init__(self, parent, pet_name: str = "meizi"):
        self.parent = parent
        self.pet_name = pet_name

        # 动画相关
        self.current_frame = 0
        self.animation_frames: List[ImageTk.PhotoImage] = []
        self.animation_timer = None
        self.animation_speed = 250  # 毫秒

        # 图片标签
        self.pet_label = None

        # 加载动画帧
        self.load_animation_frames()

        # 创建宠物标签
        self.create_pet_label()

        print(f"宠物动画初始化完成，加载了 {len(self.animation_frames)} 帧")

    def load_animation_frames(self):
        """加载动画帧"""
        # 构建图片路径
        images_dir = os.path.join("DesktopPet", "images", self.pet_name)

        if not os.path.exists(images_dir):
            print(f"警告: 宠物图片目录不存在: {images_dir}")
            # 尝试其他可能的路径
            alt_paths = [
                os.path.join("..", "DesktopPet", "images", self.pet_name),
                os.path.join("images", self.pet_name),
                os.path.join("DesktopPet", "images", "meizi")  # 默认使用 meizi
            ]

            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    images_dir = alt_path
                    print(f"使用替代路径: {images_dir}")
                    break
            else:
                print("无法找到宠物图片，将使用默认显示")
                return

        # 加载所有图片帧
        try:
            # 获取所有图片文件
            image_files = []
            for file in os.listdir(images_dir):
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                    image_files.append(file)

            # 按文件名排序
            image_files.sort()

            # 加载图片
            for image_file in image_files:
                image_path = os.path.join(images_dir, image_file)
                try:
                    # 打开图片
                    pil_image = Image.open(image_path)

                    # 调整大小以适应小挂件
                    # 保持宽高比，最大尺寸为 80x80
                    pil_image.thumbnail((80, 80), Image.Resampling.LANCZOS)

                    # 转换为 PhotoImage
                    photo = ImageTk.PhotoImage(pil_image)
                    self.animation_frames.append(photo)

                except Exception as e:
                    print(f"加载图片失败 {image_file}: {e}")

            print(f"成功加载 {len(self.animation_frames)} 个动画帧")

        except Exception as e:
            print(f"加载动画帧时出错: {e}")

    def create_pet_label(self):
        """创建宠物标签"""
        self.pet_label = tk.Label(
            self.parent,
            bg='black',  # 透明背景
            relief='flat',
            borderwidth=0
        )

        # 如果有动画帧，显示第一帧
        if self.animation_frames:
            self.pet_label.config(image=self.animation_frames[0])
        else:
            # 没有图片时显示文字
            self.pet_label.config(
                text="🤖",
                font=("Arial", 24),
                fg="white",
                bg="black"  # 使用黑色背景以便透明化
            )

        self.pet_label.pack(expand=True)

    def start_animation(self):
        """开始动画"""
        if self.animation_frames and len(self.animation_frames) > 1:
            self._animate_frame()
            print("宠物动画开始")
        else:
            print("没有足够的动画帧，跳过动画")

    def stop_animation(self):
        """停止动画"""
        if self.animation_timer:
            self.parent.after_cancel(self.animation_timer)
            self.animation_timer = None
            print("宠物动画停止")

    def show_first_frame(self):
        """显示第一帧静态图"""
        if self.animation_frames and self.pet_label:
            self.current_frame = 0
            self.pet_label.config(image=self.animation_frames[0])
            print("显示宠物第一帧静态图")

    def _animate_frame(self):
        """动画帧更新"""
        if not self.animation_frames:
            return

        # 更新当前帧
        self.current_frame = (self.current_frame + 1) % len(self.animation_frames)

        # 更新图片
        if self.pet_label:
            self.pet_label.config(image=self.animation_frames[self.current_frame])

        # 安排下一帧
        self.animation_timer = self.parent.after(self.animation_speed, self._animate_frame)

    def set_animation_speed(self, speed_ms: int):
        """设置动画速度"""
        self.animation_speed = max(50, min(1000, speed_ms))  # 限制在 50-1000ms 之间

    def get_pet_label(self):
        """获取宠物标签控件"""
        return self.pet_label

    def change_pet(self, pet_name: str):
        """更换宠物"""
        self.stop_animation()
        self.pet_name = pet_name
        self.current_frame = 0
        self.animation_frames.clear()

        # 重新加载动画帧
        self.load_animation_frames()

        # 更新显示
        if self.animation_frames and self.pet_label:
            self.pet_label.config(image=self.animation_frames[0])

        # 重新开始动画
        self.start_animation()

        print(f"切换到宠物: {pet_name}")

    def cleanup(self):
        """清理资源"""
        self.stop_animation()
        self.animation_frames.clear()
        print("宠物动画资源已清理")
