"""
测试修复的功能 v2
验证宠物动画、拖动功能和连接问题
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pet_animation():
    """测试宠物动画"""
    print("测试宠物动画...")
    
    try:
        from ui.pet_animation import PetAnimation
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("宠物动画测试")
        root.geometry("200x200")
        
        # 创建宠物动画
        pet = PetAnimation(root, "meizi")
        pet.start_animation()
        
        print(f"✅ 宠物动画创建成功，加载了 {len(pet.animation_frames)} 帧")
        
        # 显示3秒后关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 宠物动画测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_drag_functionality():
    """测试拖动功能"""
    print("\n测试拖动功能...")
    
    try:
        from utils.window_manager import WindowManager
        from core.config import config
        
        # 创建测试窗口
        root = ttk_bs.Window(title="拖动测试", size=(120, 80))
        root.overrideredirect(True)
        
        # 创建窗口管理器
        wm = WindowManager(root, config)
        
        # 创建测试标签
        label = tk.Label(root, text="拖动我", bg="lightblue")
        label.pack(expand=True, fill="both")
        
        # 设置拖动
        wm.setup_drag_events([label])
        
        print("✅ 拖动功能设置成功")
        print("请尝试拖动窗口...")
        
        # 显示5秒后关闭
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 拖动功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_connection_logic():
    """测试连接逻辑"""
    print("\n测试连接逻辑...")
    
    try:
        from core.state_manager import state_manager, AppState
        
        # 测试连接状态变化
        print("初始状态:", state_manager.app_state.value)
        
        # 模拟连接过程
        state_manager.set_app_state(AppState.CONNECTING)
        print("设置为连接中:", state_manager.app_state.value)
        
        state_manager.set_app_state(AppState.CONNECTED)
        print("设置为已连接:", state_manager.app_state.value)
        
        # 测试重试逻辑
        print("连接尝试次数:", state_manager.connection_attempts)
        print("是否应该重试:", state_manager.should_retry_connection())
        
        print("✅ 连接逻辑测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 连接逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_images_directory():
    """测试图片目录"""
    print("\n测试图片目录...")
    
    # 检查可能的图片路径
    possible_paths = [
        "DesktopPet/images/meizi",
        "../DesktopPet/images/meizi",
        "images/meizi",
        "DesktopPet/images"
    ]
    
    found_paths = []
    for path in possible_paths:
        if os.path.exists(path):
            found_paths.append(path)
            files = os.listdir(path)
            image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif'))]
            print(f"✅ 找到路径: {path} (包含 {len(image_files)} 个图片文件)")
        else:
            print(f"❌ 路径不存在: {path}")
    
    if found_paths:
        print(f"✅ 图片目录测试成功，找到 {len(found_paths)} 个有效路径")
        return True
    else:
        print("❌ 没有找到任何图片目录")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("ComfyUI TaskMonitor 修复验证测试 v2")
    print("=" * 60)
    
    tests = [
        ("图片目录检查", test_images_directory),
        ("宠物动画测试", test_pet_animation),
        ("拖动功能测试", test_drag_functionality),
        ("连接逻辑测试", test_connection_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n[{name}]")
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 通过")
            else:
                print(f"❌ {name} 失败")
        except Exception as e:
            print(f"❌ {name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序异常退出: {e}")
        input("按回车键退出...")
