"""
测试修复的功能
"""
import sys
import os

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        from main import TaskMonitorWidget
        print("✅ 主应用程序导入成功")
    except Exception as e:
        print(f"❌ 主应用程序导入失败: {e}")
        return False
    
    try:
        from ui_components import TaskIdDisplay, QueueDisplay
        print("✅ UI 组件导入成功")
    except Exception as e:
        print(f"❌ UI 组件导入失败: {e}")
        return False
    
    try:
        from window_manager import WindowManager
        print("✅ 窗口管理器导入成功")
    except Exception as e:
        print(f"❌ 窗口管理器导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from config import Config
        config = Config("test_config.json")
        
        # 测试新的配置选项
        assert config.get('show_task_id') is not None
        assert config.get('show_queue_details') is not None
        print("✅ 新配置选项存在")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n测试UI组件...")
    
    try:
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        from ui_components import TaskIdDisplay, QueueDisplay
        from api_client import TaskMonitorData, TaskStatus, QueueInfo
        
        # 创建测试窗口
        root = ttk_bs.Window()
        root.withdraw()  # 隐藏窗口
        
        # 测试任务ID显示组件
        task_id_display = TaskIdDisplay(root)
        task_id_display.update_task_id("test_task_id_12345")
        print("✅ 任务ID显示组件创建成功")
        
        # 测试队列显示组件
        queue_display = QueueDisplay(root)
        
        # 创建测试数据
        test_data = TaskMonitorData()
        test_data.queue = QueueInfo(
            running_count=1,
            pending_count=2,
            running=[{"prompt_id": "test123", "nodes_in_prompt": 5, "client_id": "client1"}],
            pending=[{"prompt_id": "test456", "nodes_in_prompt": 3}]
        )
        
        queue_display.update_queue(test_data)
        queue_display.set_show_details(True)
        print("✅ 队列显示组件创建成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("测试修复的功能")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_config),
        ("UI组件测试", test_ui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n[{name}]")
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 通过")
            else:
                print(f"❌ {name} 失败")
        except Exception as e:
            print(f"❌ {name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    main()
