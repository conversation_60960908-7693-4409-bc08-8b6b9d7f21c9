"""
ComfyUI TaskMonitor 桌面小部件
主应用程序入口
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
import time
from typing import Optional

from config import Config
from api_client import ComfyUIApiClient, TaskMonitorData, TaskStatus
from window_manager import WindowManager
from ui_components import (
    StatusIndicator, ProgressDisplay, QueueDisplay,
    ExecutionTimeDisplay, ErrorDisplay, ControlPanel
)
from settings_window import SettingsWindow

try:
    from plyer import notification
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False

class TaskMonitorWidget:
    def __init__(self):
        # 初始化配置
        self.config = Config()

        # 初始化 API 客户端
        self.api_client = ComfyUIApiClient(
            base_url=self.config.get('server_url'),
            timeout=self.config.get('connection_timeout')
        )

        # 状态变量
        self.last_status = TaskStatus.DISCONNECTED
        self.update_thread = None
        self.running = True

        # 创建主窗口
        self.setup_window()

        # 创建界面
        self.setup_ui()

        # 设置窗口管理器
        self.window_manager = WindowManager(self.root, self.config)
        self.window_manager.setup_borderless_window()
        self.window_manager.restore_window_position()

        # 启动数据更新线程
        self.start_update_thread()

    def setup_window(self):
        """设置主窗口"""
        self.root = ttk_bs.Window(
            title="ComfyUI TaskMonitor",
            themename=self.config.get('theme', 'darkly'),
            size=(
                self.config.get('window_width', 400),
                self.config.get('window_height', 300)
            ),
            resizable=(True, True),
            minsize=(300, 200)
        )

        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置应用程序图标
            pass
        except:
            pass

    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ttk_bs.Frame(self.root, padding=10)
        main_frame.pack(fill="both", expand=True)

        # 标题栏（用于拖动）
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill="x", pady=(0, 10))

        title_label = ttk_bs.Label(
            title_frame,
            text="ComfyUI 任务监控",
            font=("Microsoft YaHei UI", 11, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side="left")

        # 控制面板
        self.control_panel = ControlPanel(
            title_frame,
            on_settings=self.show_settings,
            on_close=self.close_application
        )
        self.control_panel.pack(side="right")

        # 状态指示器
        self.status_indicator = StatusIndicator(main_frame)
        self.status_indicator.pack(fill="x", pady=(0, 10))

        # 进度显示
        self.progress_display = ProgressDisplay(main_frame)
        self.progress_display.pack(fill="x", pady=(0, 10))

        # 队列显示
        if self.config.get('show_queue_info', True):
            self.queue_display = QueueDisplay(main_frame)
            self.queue_display.pack(fill="x", pady=(0, 5))

        # 执行时间显示
        if self.config.get('show_execution_time', True):
            self.time_display = ExecutionTimeDisplay(main_frame)
            self.time_display.pack(fill="x", pady=(0, 5))

        # 错误信息显示
        self.error_display = ErrorDisplay(main_frame)
        # 错误显示默认隐藏，有错误时才显示

    def start_update_thread(self):
        """启动数据更新线程"""
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

    def update_loop(self):
        """数据更新循环"""
        while self.running:
            try:
                # 获取数据
                data = self.api_client.get_status()

                # 在主线程中更新 UI
                self.root.after(0, self.update_ui, data)

                # 检查状态变化
                if data.status != self.last_status:
                    self.root.after(0, self.handle_status_change, self.last_status, data.status)
                    self.last_status = data.status

                # 等待下次更新
                interval = self.config.get('refresh_interval', 1000) / 1000.0
                time.sleep(interval)

            except Exception as e:
                print(f"更新循环错误: {e}")
                time.sleep(1)

    def update_ui(self, data: TaskMonitorData):
        """更新用户界面"""
        try:
            # 更新状态指示器
            self.status_indicator.update_status(data.status)

            # 更新进度显示
            self.progress_display.update_progress(data)

            # 更新队列信息
            if hasattr(self, 'queue_display'):
                self.queue_display.update_queue(data)

            # 更新执行时间
            if hasattr(self, 'time_display'):
                self.time_display.update_time(data.execution_time)

            # 更新错误信息
            self.error_display.update_errors(data.error_info)

        except Exception as e:
            print(f"UI 更新错误: {e}")

    def handle_status_change(self, old_status: TaskStatus, new_status: TaskStatus):
        """处理状态变化"""
        # 工作流完成通知
        if (old_status == TaskStatus.RUNNING and
            new_status == TaskStatus.COMPLETED and
            self.config.get('enable_notifications', True)):
            self.show_completion_notification()

    def show_completion_notification(self):
        """显示完成通知"""
        if NOTIFICATIONS_AVAILABLE:
            try:
                notification.notify(
                    title="ComfyUI TaskMonitor",
                    message="工作流执行完成！",
                    timeout=5
                )
            except Exception as e:
                print(f"通知显示失败: {e}")

        # 如果启用了完成弹窗
        if self.config.get('show_completion_popup', True):
            self.show_completion_popup()

    def show_completion_popup(self):
        """显示完成弹窗"""
        popup = ttk_bs.Toplevel(self.root)
        popup.title("任务完成")
        popup.geometry("300x150")
        popup.resizable(False, False)
        popup.transient(self.root)
        popup.grab_set()

        # 居中显示
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() - popup.winfo_width()) // 2
        y = (popup.winfo_screenheight() - popup.winfo_height()) // 2
        popup.geometry(f"+{x}+{y}")

        # 内容
        frame = ttk_bs.Frame(popup, padding=20)
        frame.pack(fill="both", expand=True)

        ttk_bs.Label(
            frame,
            text="🎉 工作流执行完成！",
            font=("Microsoft YaHei UI", 12, "bold"),
            bootstyle="success"
        ).pack(pady=(0, 20))

        ttk_bs.Button(
            frame,
            text="确定",
            bootstyle="primary",
            command=popup.destroy
        ).pack()

        # 3秒后自动关闭
        popup.after(3000, popup.destroy)

    def show_settings(self):
        """显示设置窗口"""
        SettingsWindow(self.root, self.config, self.apply_settings)

    def apply_settings(self):
        """应用设置更改"""
        try:
            # 更新 API 客户端配置
            self.api_client.update_config(
                self.config.get('server_url'),
                self.config.get('connection_timeout')
            )

            # 更新窗口管理器设置
            self.window_manager.set_always_on_top(self.config.get('always_on_top'))
            self.window_manager.set_transparency(self.config.get('transparency'))

            # 重新创建界面组件（如果需要）
            self.refresh_ui()

        except Exception as e:
            print(f"应用设置时出错: {e}")

    def refresh_ui(self):
        """刷新用户界面"""
        # 这里可以根据新设置重新创建或更新UI组件
        # 由于某些更改需要重启应用程序，这里只做基本的更新
        pass

    def close_application(self):
        """关闭应用程序"""
        self.running = False

        # 保存配置
        self.window_manager.save_window_position()
        self.config.save_config()

        # 关闭窗口
        self.root.quit()
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.close_application)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.close_application()

def main():
    """主函数"""
    try:
        app = TaskMonitorWidget()
        app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
