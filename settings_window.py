"""
设置窗口模块
提供应用程序配置界面
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from tkinter import colorchooser, messagebox
from config import Config
from typing import Callable

class SettingsWindow:
    def __init__(self, parent, config: Config, on_apply: Callable = None):
        self.parent = parent
        self.config = config
        self.on_apply = on_apply
        
        # 创建设置窗口
        self.window = ttk_bs.Toplevel(parent)
        self.window.title("设置")
        self.window.geometry("500x600")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.setup_ui()
        
        # 加载当前配置
        self.load_current_config()
    
    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() - 500) // 2
        y = (self.window.winfo_screenheight() - 600) // 2
        self.window.geometry(f"500x600+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建笔记本控件（标签页）
        notebook = ttk_bs.Notebook(self.window)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 服务器设置标签页
        self.setup_server_tab(notebook)
        
        # 窗口设置标签页
        self.setup_window_tab(notebook)
        
        # 外观设置标签页
        self.setup_appearance_tab(notebook)
        
        # 通知设置标签页
        self.setup_notification_tab(notebook)
        
        # 按钮框架
        button_frame = ttk_bs.Frame(self.window)
        button_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # 按钮
        ttk_bs.Button(
            button_frame,
            text="应用",
            bootstyle="primary",
            command=self.apply_settings
        ).pack(side="right", padx=(5, 0))
        
        ttk_bs.Button(
            button_frame,
            text="取消",
            bootstyle="secondary",
            command=self.window.destroy
        ).pack(side="right")
        
        ttk_bs.Button(
            button_frame,
            text="重置默认",
            bootstyle="warning",
            command=self.reset_to_default
        ).pack(side="left")
    
    def setup_server_tab(self, notebook):
        """设置服务器标签页"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="服务器")
        
        # 服务器地址
        ttk_bs.Label(frame, text="服务器地址:").pack(anchor="w", pady=(10, 5))
        self.server_url_var = tk.StringVar()
        ttk_bs.Entry(
            frame,
            textvariable=self.server_url_var,
            width=50
        ).pack(fill="x", pady=(0, 10))
        
        # 刷新间隔
        ttk_bs.Label(frame, text="刷新间隔 (毫秒):").pack(anchor="w", pady=(0, 5))
        self.refresh_interval_var = tk.IntVar()
        ttk_bs.Entry(
            frame,
            textvariable=self.refresh_interval_var,
            width=20
        ).pack(anchor="w", pady=(0, 10))
        
        # 连接超时
        ttk_bs.Label(frame, text="连接超时 (秒):").pack(anchor="w", pady=(0, 5))
        self.connection_timeout_var = tk.IntVar()
        ttk_bs.Entry(
            frame,
            textvariable=self.connection_timeout_var,
            width=20
        ).pack(anchor="w", pady=(0, 10))
        
        # 重试间隔
        ttk_bs.Label(frame, text="重试间隔 (毫秒):").pack(anchor="w", pady=(0, 5))
        self.retry_interval_var = tk.IntVar()
        ttk_bs.Entry(
            frame,
            textvariable=self.retry_interval_var,
            width=20
        ).pack(anchor="w", pady=(0, 10))
    
    def setup_window_tab(self, notebook):
        """设置窗口标签页"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="窗口")
        
        # 窗口大小
        size_frame = ttk_bs.Frame(frame)
        size_frame.pack(fill="x", pady=(10, 10))
        
        ttk_bs.Label(size_frame, text="窗口大小:").pack(anchor="w")
        
        size_input_frame = ttk_bs.Frame(size_frame)
        size_input_frame.pack(anchor="w", pady=(5, 0))
        
        ttk_bs.Label(size_input_frame, text="宽度:").pack(side="left")
        self.window_width_var = tk.IntVar()
        ttk_bs.Entry(
            size_input_frame,
            textvariable=self.window_width_var,
            width=10
        ).pack(side="left", padx=(5, 10))
        
        ttk_bs.Label(size_input_frame, text="高度:").pack(side="left")
        self.window_height_var = tk.IntVar()
        ttk_bs.Entry(
            size_input_frame,
            textvariable=self.window_height_var,
            width=10
        ).pack(side="left", padx=(5, 0))
        
        # 置顶选项
        self.always_on_top_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="窗口置顶",
            variable=self.always_on_top_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(10, 5))
        
        # 透明度
        ttk_bs.Label(frame, text="透明度:").pack(anchor="w", pady=(10, 5))
        self.transparency_var = tk.DoubleVar()
        transparency_scale = ttk_bs.Scale(
            frame,
            from_=0.1,
            to=1.0,
            variable=self.transparency_var,
            orient="horizontal",
            length=300
        )
        transparency_scale.pack(anchor="w", pady=(0, 5))
        
        self.transparency_label = ttk_bs.Label(frame, text="90%")
        self.transparency_label.pack(anchor="w")
        
        # 绑定透明度变化事件
        transparency_scale.config(command=self.on_transparency_change)
        
        # 边缘吸附
        self.snap_to_edge_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="边缘吸附",
            variable=self.snap_to_edge_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(10, 5))
        
        # 吸附距离
        ttk_bs.Label(frame, text="吸附距离 (像素):").pack(anchor="w", pady=(10, 5))
        self.snap_distance_var = tk.IntVar()
        ttk_bs.Entry(
            frame,
            textvariable=self.snap_distance_var,
            width=20
        ).pack(anchor="w", pady=(0, 10))
    
    def setup_appearance_tab(self, notebook):
        """设置外观标签页"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="外观")
        
        # 主题选择
        ttk_bs.Label(frame, text="主题:").pack(anchor="w", pady=(10, 5))
        self.theme_var = tk.StringVar()
        theme_combo = ttk_bs.Combobox(
            frame,
            textvariable=self.theme_var,
            values=["darkly", "flatly", "litera", "minty", "lumen", "sandstone", "yeti", "pulse", "united", "morph"],
            state="readonly",
            width=30
        )
        theme_combo.pack(anchor="w", pady=(0, 10))
        
        # 字体设置
        font_frame = ttk_bs.Frame(frame)
        font_frame.pack(fill="x", pady=(10, 10))
        
        ttk_bs.Label(font_frame, text="字体:").pack(anchor="w")
        
        font_input_frame = ttk_bs.Frame(font_frame)
        font_input_frame.pack(anchor="w", pady=(5, 0))
        
        ttk_bs.Label(font_input_frame, text="字体族:").pack(side="left")
        self.font_family_var = tk.StringVar()
        ttk_bs.Entry(
            font_input_frame,
            textvariable=self.font_family_var,
            width=20
        ).pack(side="left", padx=(5, 10))
        
        ttk_bs.Label(font_input_frame, text="大小:").pack(side="left")
        self.font_size_var = tk.IntVar()
        ttk_bs.Entry(
            font_input_frame,
            textvariable=self.font_size_var,
            width=5
        ).pack(side="left", padx=(5, 0))
        
        # 紧凑模式
        self.compact_mode_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="紧凑模式",
            variable=self.compact_mode_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(10, 5))
        
        # 动画效果
        self.enable_animations_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="启用动画效果",
            variable=self.enable_animations_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(5, 5))
    
    def setup_notification_tab(self, notebook):
        """设置通知标签页"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="通知")
        
        # 启用通知
        self.enable_notifications_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="启用通知",
            variable=self.enable_notifications_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(10, 5))
        
        # 通知声音
        self.notification_sound_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="通知声音",
            variable=self.notification_sound_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(5, 5))
        
        # 完成弹窗
        self.show_completion_popup_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="显示完成弹窗",
            variable=self.show_completion_popup_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(5, 5))
        
        # 显示选项
        ttk_bs.Label(frame, text="显示选项:").pack(anchor="w", pady=(20, 5))
        
        self.show_queue_info_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="显示队列信息",
            variable=self.show_queue_info_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(5, 5))
        
        self.show_execution_time_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="显示执行时间",
            variable=self.show_execution_time_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(5, 5))
        
        self.show_node_details_var = tk.BooleanVar()
        ttk_bs.Checkbutton(
            frame,
            text="显示节点详情",
            variable=self.show_node_details_var,
            bootstyle="primary"
        ).pack(anchor="w", pady=(5, 5))
    
    def on_transparency_change(self, value):
        """透明度变化回调"""
        percentage = int(float(value) * 100)
        self.transparency_label.config(text=f"{percentage}%")
    
    def load_current_config(self):
        """加载当前配置"""
        # 服务器设置
        self.server_url_var.set(self.config.get('server_url'))
        self.refresh_interval_var.set(self.config.get('refresh_interval'))
        self.connection_timeout_var.set(self.config.get('connection_timeout'))
        self.retry_interval_var.set(self.config.get('retry_interval'))
        
        # 窗口设置
        self.window_width_var.set(self.config.get('window_width'))
        self.window_height_var.set(self.config.get('window_height'))
        self.always_on_top_var.set(self.config.get('always_on_top'))
        self.transparency_var.set(self.config.get('transparency'))
        self.snap_to_edge_var.set(self.config.get('snap_to_edge'))
        self.snap_distance_var.set(self.config.get('snap_distance'))
        
        # 外观设置
        self.theme_var.set(self.config.get('theme'))
        self.font_family_var.set(self.config.get('font_family'))
        self.font_size_var.set(self.config.get('font_size'))
        self.compact_mode_var.set(self.config.get('compact_mode'))
        self.enable_animations_var.set(self.config.get('enable_animations'))
        
        # 通知设置
        self.enable_notifications_var.set(self.config.get('enable_notifications'))
        self.notification_sound_var.set(self.config.get('notification_sound'))
        self.show_completion_popup_var.set(self.config.get('show_completion_popup'))
        self.show_queue_info_var.set(self.config.get('show_queue_info'))
        self.show_execution_time_var.set(self.config.get('show_execution_time'))
        self.show_node_details_var.set(self.config.get('show_node_details'))
        
        # 更新透明度标签
        self.on_transparency_change(self.transparency_var.get())
    
    def apply_settings(self):
        """应用设置"""
        try:
            # 更新配置
            updates = {
                # 服务器设置
                'server_url': self.server_url_var.get(),
                'refresh_interval': self.refresh_interval_var.get(),
                'connection_timeout': self.connection_timeout_var.get(),
                'retry_interval': self.retry_interval_var.get(),
                
                # 窗口设置
                'window_width': self.window_width_var.get(),
                'window_height': self.window_height_var.get(),
                'always_on_top': self.always_on_top_var.get(),
                'transparency': self.transparency_var.get(),
                'snap_to_edge': self.snap_to_edge_var.get(),
                'snap_distance': self.snap_distance_var.get(),
                
                # 外观设置
                'theme': self.theme_var.get(),
                'font_family': self.font_family_var.get(),
                'font_size': self.font_size_var.get(),
                'compact_mode': self.compact_mode_var.get(),
                'enable_animations': self.enable_animations_var.get(),
                
                # 通知设置
                'enable_notifications': self.enable_notifications_var.get(),
                'notification_sound': self.notification_sound_var.get(),
                'show_completion_popup': self.show_completion_popup_var.get(),
                'show_queue_info': self.show_queue_info_var.get(),
                'show_execution_time': self.show_execution_time_var.get(),
                'show_node_details': self.show_node_details_var.get(),
            }
            
            self.config.update(updates)
            self.config.save_config()
            
            # 调用回调函数
            if self.on_apply:
                self.on_apply()
            
            messagebox.showinfo("设置", "设置已保存！\n某些设置需要重启应用程序才能生效。")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置时出错：{str(e)}")
    
    def reset_to_default(self):
        """重置为默认设置"""
        if messagebox.askyesno("确认", "确定要重置为默认设置吗？"):
            self.config.reset_to_default()
            self.load_current_config()
