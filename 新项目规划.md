# ComfyUI TaskMonitor 桌面小挂件 - 重新规划

## 🎯 项目概述

基于更新的需求文档，重新设计一个桌面小挂件应用程序，结合 desktoppet（桌面宠物）的设计理念，为 ComfyUI 工作流监控提供简洁而强大的桌面体验。

## 🏗️ 核心设计理念

### 双模式界面设计
1. **小挂件模式（默认）**
   - 显示简洁的状态指示器
   - 只显示最关键的信息（状态、进度百分比）
   - 小巧精致，不占用太多桌面空间

2. **详细信息模式（点击展开）**
   - 显示完整的监控信息
   - 包含所有 API 返回的数据
   - 可配置显示哪些信息

### 桌面宠物特性
- 无边框窗口设计
- 可拖动和边缘吸附
- 透明背景支持
- 动画效果和闪烁提醒
- 始终置顶选项

## 📁 新的文件结构

```
ComfyUI_TaskMonitor_Widget/
├── core/                          # 核心模块
│   ├── __init__.py
│   ├── config.py                  # 配置管理
│   ├── api_client.py             # API 客户端
│   └── state_manager.py          # 状态管理
├── ui/                           # 界面模块
│   ├── __init__.py
│   ├── widget_mini.py            # 小挂件界面
│   ├── widget_detailed.py        # 详细信息界面
│   ├── settings_window.py        # 设置窗口
│   └── components/               # UI 组件
│       ├── __init__.py
│       ├── status_indicator.py   # 状态指示器
│       ├── progress_ring.py      # 圆形进度条
│       └── info_panels.py        # 信息面板
├── utils/                        # 工具模块
│   ├── __init__.py
│   ├── window_manager.py         # 窗口管理
│   ├── animation.py              # 动画效果
│   └── notifications.py         # 通知系统
├── assets/                       # 资源文件
│   ├── icons/                    # 图标
│   └── themes/                   # 主题配置
├── main.py                       # 主程序入口
├── requirements.txt              # 依赖列表
└── README.md                     # 说明文档
```

## 🎨 界面设计方案

### 小挂件模式（默认）
```
┌─────────────┐
│    ComfyUI   │  <- 标题/状态文字
│      ●       │  <- 状态指示灯
│   ████████   │  <- 简化进度条
│     85%      │  <- 进度百分比
└─────────────┘
尺寸：120x80 像素
```

### 详细信息模式（点击展开）
```
┌─────────────────────────────┐
│ ComfyUI TaskMonitor    [⚙][✕] │
├─────────────────────────────┤
│ 状态: 运行中 ●              │
│ 任务ID: abc123...           │
├─────────────────────────────┤
│ 工作流进度: 18/25 节点       │
│ ████████████░░░ 72%         │
├─────────────────────────────┤
│ 当前任务: 15/20 步骤         │
│ 节点: KSampler              │
├─────────────────────────────┤
│ 队列: 运行中 2, 等待中 3     │
│ 执行时间: 125.7 秒          │
└─────────────────────────────┘
尺寸：300x200 像素（可调整）
```

## 🔧 核心功能实现

### 1. 双模式切换
- 点击小挂件展开为详细模式
- 点击详细模式外部或按 ESC 收缩为小挂件
- 平滑的展开/收缩动画

### 2. 状态指示系统
- 颜色编码的状态指示灯
- 动态进度环/条
- 闪烁动画（完成时）

### 3. 桌面交互
- 拖动功能（在非交互区域）
- 边缘自动吸附
- 右键菜单（设置、退出等）

### 4. 动画系统
- 状态变化动画
- 进度更新动画
- 完成时闪烁3次
- 展开/收缩动画

## 📊 数据流设计

```
API Client → State Manager → UI Controller → Widget Display
     ↑            ↓              ↓              ↓
Config ←→ Animation ←→ Notifications ←→ Window Manager
```

## 🎯 实现优先级

### Phase 1: 核心功能
1. API 客户端和数据解析
2. 基础的小挂件界面
3. 状态指示和简单进度显示
4. 点击展开功能

### Phase 2: 桌面集成
1. 无边框窗口和拖动
2. 边缘吸附功能
3. 透明度和置顶设置
4. 基础动画效果

### Phase 3: 高级功能
1. 详细信息界面
2. 设置和配置系统
3. 完成通知和闪烁
4. 主题和自定义样式

### Phase 4: 优化和完善
1. 性能优化
2. 错误处理完善
3. 用户体验优化
4. 文档和测试

## 🎨 视觉设计原则

### 小挂件模式
- **极简主义**：只显示最重要的信息
- **一目了然**：状态和进度一眼可见
- **不干扰**：小巧精致，不影响工作

### 详细模式
- **信息丰富**：显示所有可用数据
- **层次清晰**：信息分组和优先级明确
- **易于操作**：设置和控制便于访问

### 动画效果
- **自然流畅**：符合用户预期的动画
- **有意义**：动画传达状态变化
- **可控制**：用户可以禁用动画

## 🔧 技术选型

### 核心技术栈
- **Python 3.8+**
- **Tkinter + ttkbootstrap**：现代化的 UI 框架
- **requests**：HTTP 客户端
- **threading**：后台数据更新
- **json**：配置和数据处理

### 特殊功能库
- **PIL/Pillow**：图像处理和图标
- **plyer**：跨平台通知（可选）
- **win32gui**：Windows 特定功能（可选）

## 📝 配置系统

### 基础配置
```json
{
  "server": {
    "url": "http://localhost:8188",
    "refresh_interval": 1000,
    "timeout": 5
  },
  "ui": {
    "mode": "mini",
    "always_on_top": true,
    "transparency": 0.9,
    "theme": "dark"
  },
  "widget": {
    "size": {"width": 120, "height": 80},
    "position": {"x": 100, "y": 100},
    "auto_hide": false
  },
  "notifications": {
    "enabled": true,
    "flash_on_complete": true,
    "sound": false
  }
}
```

## 🎯 用户体验目标

1. **即开即用**：无需复杂配置即可使用
2. **不干扰工作**：小挂件模式不影响日常工作
3. **信息及时**：重要状态变化立即可见
4. **高度可定制**：满足不同用户的个性化需求
5. **稳定可靠**：处理各种异常情况

这个重新规划的方案更符合桌面小挂件的设计理念，提供了简洁的默认体验和丰富的详细功能。
