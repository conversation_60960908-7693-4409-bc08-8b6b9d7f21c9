ComfyUI-TaskMonitor是一个监听comfyui工作流程进度的扩展插件，它提供了一个关键的 API 接口，可以获取任务的实时状态和进度信息：
1.API 接口: GET /task_monitor/status
2.返回数据格式: JSON
3.包含信息:
-任务 ID
-状态 (空闲, 运行中, 完成, 错误, 排队中)
-队列信息 (运行中和等待中的任务，包括提示 ID、节点数量、客户端 ID)
-当前任务进度 (节点 ID, 节点类型, 当前步骤, 总步骤, 文本消息)
-工作流进度 (总节点数, 已执行节点数, 最后执行的节点 ID)
-执行时间
-错误信息
现在我需要你使用 Tkinter (及其扩展如：ttkbootstrap) 结合desktoppet这个项目编写一个 Windows 桌面小挂件来调用这个接口来监听 ComfyUI 工作流的以上信息。
需求如下：
1.默认只显示小挂件，只有用户单击小挂件才会显示详细信息。
2.用户可设置是否需要置顶显示。
3.用户可以设置透明背景，及小部件的透明度。
4.用户可自定义样式,自定义进度条、标签的外观及其它控件的外观.
5.允许用户配置 ComfyUI 服务器地址、刷新频率等.
6.用户可自定义小部件的大小和位置，字体和颜色，动画效果.
7.工作流进度完成时提示信息，小挂件并闪烁3次.
8.为了实现“小部件”的感觉，需要移除窗口边框和标题栏，并自行实现拖动等功能如果靠近窗口边缘则自动吸附到窗口边缘。
9.如果 ComfyUI 还没有完全启动并运行该 API 端点，小部件的初始几次请求可能会失败。你需要在小部件的代码中处理这种情况（例如，显示“连接中...”或“等待 ComfyUI...”，并进行重试）。


