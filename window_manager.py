"""
窗口管理模块
处理无边框窗口的拖动、吸附等功能
"""
import tkinter as tk
from typing import Tuple, Optional

class WindowManager:
    def __init__(self, window: tk.Tk, config):
        self.window = window
        self.config = config
        self.dragging = False
        self.start_x = 0
        self.start_y = 0
        self.snap_distance = config.get('snap_distance', 20)

        # 绑定拖动事件
        self.setup_drag_events()

    def setup_drag_events(self):
        """设置拖动事件绑定"""
        self.window.bind('<Button-1>', self.start_drag)
        self.window.bind('<B1-Motion>', self.on_drag)
        self.window.bind('<ButtonRelease-1>', self.stop_drag)

        # 为所有子控件也绑定拖动事件
        self._bind_drag_to_children(self.window)

    def rebind_drag_events(self):
        """重新绑定拖动事件（在UI更新后调用）"""
        self._bind_drag_to_children(self.window)

    def _bind_drag_to_children(self, parent):
        """递归为所有子控件绑定拖动事件"""
        for child in parent.winfo_children():
            # 跳过某些不应该响应拖动的控件
            widget_class = child.winfo_class()
            if widget_class in ['Entry', 'Text', 'Listbox', 'Scrollbar', 'Button', 'Checkbutton', 'Scale', 'Combobox']:
                continue

            # 绑定拖动事件
            child.bind('<Button-1>', self.start_drag, add='+')
            child.bind('<B1-Motion>', self.on_drag, add='+')
            child.bind('<ButtonRelease-1>', self.stop_drag, add='+')

            # 递归处理子控件
            self._bind_drag_to_children(child)

    def start_drag(self, event):
        """开始拖动"""
        self.dragging = True
        self.start_x = event.x_root
        self.start_y = event.y_root

    def on_drag(self, event):
        """拖动过程中"""
        if not self.dragging:
            return

        # 计算新位置
        x = self.window.winfo_x() + (event.x_root - self.start_x)
        y = self.window.winfo_y() + (event.y_root - self.start_y)

        # 如果启用了边缘吸附，检查是否需要吸附
        if self.config.get('snap_to_edge', True):
            x, y = self.apply_edge_snapping(x, y)

        # 移动窗口
        self.window.geometry(f"+{x}+{y}")

        # 更新起始位置
        self.start_x = event.x_root
        self.start_y = event.y_root

    def stop_drag(self, event):
        """停止拖动"""
        self.dragging = False

        # 保存窗口位置到配置
        self.save_window_position()

    def apply_edge_snapping(self, x: int, y: int) -> Tuple[int, int]:
        """应用边缘吸附"""
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()

        # 左边缘吸附
        if x <= self.snap_distance:
            x = 0

        # 右边缘吸附
        elif x + window_width >= screen_width - self.snap_distance:
            x = screen_width - window_width

        # 上边缘吸附
        if y <= self.snap_distance:
            y = 0

        # 下边缘吸附
        elif y + window_height >= screen_height - self.snap_distance:
            y = screen_height - window_height

        return x, y

    def save_window_position(self):
        """保存窗口位置到配置"""
        geometry = self.window.geometry()
        self.config.set_window_geometry(geometry)

    def set_always_on_top(self, on_top: bool):
        """设置窗口置顶"""
        self.window.attributes('-topmost', on_top)
        self.config.set('always_on_top', on_top)

    def set_transparency(self, alpha: float):
        """设置窗口透明度"""
        # 确保透明度在有效范围内
        alpha = max(0.1, min(1.0, alpha))
        self.window.attributes('-alpha', alpha)
        self.config.set('transparency', alpha)

    def toggle_always_on_top(self):
        """切换置顶状态"""
        current = self.config.get('always_on_top', True)
        self.set_always_on_top(not current)

    def center_window(self):
        """将窗口居中显示"""
        self.window.update_idletasks()

        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()

        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.window.geometry(f"+{x}+{y}")
        self.save_window_position()

    def move_to_corner(self, corner: str):
        """移动窗口到指定角落"""
        self.window.update_idletasks()

        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()

        positions = {
            'top-left': (0, 0),
            'top-right': (screen_width - window_width, 0),
            'bottom-left': (0, screen_height - window_height),
            'bottom-right': (screen_width - window_width, screen_height - window_height)
        }

        if corner in positions:
            x, y = positions[corner]
            self.window.geometry(f"+{x}+{y}")
            self.save_window_position()

    def setup_borderless_window(self):
        """设置无边框窗口"""
        self.window.overrideredirect(True)

        # 设置初始透明度和置顶
        self.set_transparency(self.config.get('transparency', 0.9))
        self.set_always_on_top(self.config.get('always_on_top', True))

    def restore_window_position(self):
        """恢复窗口位置"""
        geometry = self.config.get_window_geometry()
        self.window.geometry(geometry)
