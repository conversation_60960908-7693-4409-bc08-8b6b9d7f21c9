"""
窗口管理器
处理无边框窗口的拖动、吸附、透明度等功能
"""
import tkinter as tk
from typing import Tuple, List, Optional

class WindowManager:
    """窗口管理器"""

    def __init__(self, window: tk.Tk, config_manager):
        self.window = window
        self.config = config_manager

        # 拖动相关
        self.dragging = False
        self.start_x = 0
        self.start_y = 0
        self.drag_widgets: List[tk.Widget] = []

        # 窗口状态
        self.is_mini_mode = True

        print("窗口管理器初始化完成")

    def setup_borderless_window(self):
        """设置无边框窗口"""
        # 移除窗口边框和标题栏
        self.window.overrideredirect(True)

        # 设置窗口属性
        self.apply_window_settings()

        # 恢复窗口位置
        self.restore_window_position()

        print("无边框窗口设置完成")

    def apply_window_settings(self):
        """应用窗口设置"""
        # 设置置顶
        self.window.attributes('-topmost', self.config.ui.always_on_top)

        # 设置透明度
        self.window.attributes('-alpha', self.config.ui.transparency)

        # 设置窗口大小
        self.update_window_size()

    def update_window_size(self):
        """更新窗口大小"""
        if self.is_mini_mode:
            width, height = self.config.widget.mini_size
        else:
            width, height = self.config.widget.detailed_size

        x, y = self.config.widget.position
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def restore_window_position(self):
        """恢复窗口位置"""
        geometry = self.config.get_window_geometry("mini" if self.is_mini_mode else "detailed")
        self.window.geometry(geometry)

    def setup_drag_events(self, draggable_widgets: List[tk.Widget]):
        """设置拖动事件"""
        self.drag_widgets = draggable_widgets

        # 为主窗口绑定拖动事件
        self.window.bind('<Button-1>', self.start_drag)
        self.window.bind('<B1-Motion>', self.on_drag)
        self.window.bind('<ButtonRelease-1>', self.stop_drag)

        # 为可拖动控件绑定事件
        for widget in draggable_widgets:
            widget.bind('<Button-1>', self.start_drag)
            widget.bind('<B1-Motion>', self.on_drag)
            widget.bind('<ButtonRelease-1>', self.stop_drag)

        print(f"已为 {len(draggable_widgets)} 个控件设置拖动事件")

    def start_drag(self, event):
        """开始拖动 - 参考 pet.py 的实现"""
        self.dragging = True
        # 记录鼠标相对于窗口的偏移
        self.start_x = event.x_root - self.window.winfo_x()
        self.start_y = event.y_root - self.window.winfo_y()
        print(f"开始拖动: 鼠标位置({event.x_root}, {event.y_root}), 窗口位置({self.window.winfo_x()}, {self.window.winfo_y()})")

    def on_drag(self, event):
        """拖动过程中 - 参考 pet.py 的实现"""
        if not self.dragging:
            return

        # 计算新的窗口位置（鼠标位置减去偏移）
        x = event.x_root - self.start_x
        y = event.y_root - self.start_y

        # 应用边缘吸附
        if self.config.widget.snap_to_edge:
            x, y = self.apply_edge_snapping(x, y)

        # 移动窗口
        self.window.geometry(f"+{x}+{y}")
        print(f"拖动中: 新位置({x}, {y})")

    def stop_drag(self, event):
        """停止拖动"""
        if self.dragging:
            self.dragging = False
            self.save_window_position()
            print(f"停止拖动: 最终位置({self.window.winfo_x()}, {self.window.winfo_y()})")

    def apply_edge_snapping(self, x: int, y: int) -> Tuple[int, int]:
        """应用边缘吸附"""
        snap_distance = self.config.widget.snap_distance
        release_distance = snap_distance + 10

        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()

        current_x = self.window.winfo_x()
        current_y = self.window.winfo_y()

        # 左边缘吸附
        if x <= snap_distance and current_x != 0:
            x = 0
        elif current_x == 0 and x < release_distance:
            x = 0

        # 右边缘吸附
        elif x + window_width >= screen_width - snap_distance and current_x != screen_width - window_width:
            x = screen_width - window_width
        elif current_x == screen_width - window_width and x + window_width > screen_width - release_distance:
            x = screen_width - window_width

        # 上边缘吸附
        if y <= snap_distance and current_y != 0:
            y = 0
        elif current_y == 0 and y < release_distance:
            y = 0

        # 下边缘吸附
        elif y + window_height >= screen_height - snap_distance and current_y != screen_height - window_height:
            y = screen_height - window_height
        elif current_y == screen_height - window_height and y + window_height > screen_height - release_distance:
            y = screen_height - window_height

        return x, y

    def save_window_position(self):
        """保存窗口位置"""
        x = self.window.winfo_x()
        y = self.window.winfo_y()
        self.config.set_window_position(x, y)
        print(f"保存窗口位置: {x}, {y}")

    def switch_to_mini_mode(self):
        """切换到小挂件模式"""
        if not self.is_mini_mode:
            self.is_mini_mode = True
            self.config.ui.mode = "mini"
            self.update_window_size()
            print("切换到小挂件模式")

    def switch_to_detailed_mode(self):
        """切换到详细模式"""
        if self.is_mini_mode:
            self.is_mini_mode = False
            self.config.ui.mode = "detailed"
            self.update_window_size()
            print("切换到详细模式")

    def toggle_mode(self):
        """切换模式"""
        if self.is_mini_mode:
            self.switch_to_detailed_mode()
        else:
            self.switch_to_mini_mode()

    def set_always_on_top(self, on_top: bool):
        """设置窗口置顶"""
        self.window.attributes('-topmost', on_top)
        self.config.ui.always_on_top = on_top
        print(f"设置窗口置顶: {on_top}")

    def set_transparency(self, alpha: float):
        """设置窗口透明度"""
        alpha = max(0.1, min(1.0, alpha))
        self.window.attributes('-alpha', alpha)
        self.config.ui.transparency = alpha
        print(f"设置窗口透明度: {alpha}")

    def toggle_always_on_top(self):
        """切换置顶状态"""
        current = self.config.ui.always_on_top
        self.set_always_on_top(not current)

    def center_window(self):
        """将窗口居中显示"""
        self.window.update_idletasks()

        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()

        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.window.geometry(f"+{x}+{y}")
        self.save_window_position()
        print("窗口已居中")

    def move_to_corner(self, corner: str):
        """移动窗口到指定角落"""
        self.window.update_idletasks()

        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()

        positions = {
            'top-left': (0, 0),
            'top-right': (screen_width - window_width, 0),
            'bottom-left': (0, screen_height - window_height),
            'bottom-right': (screen_width - window_width, screen_height - window_height)
        }

        if corner in positions:
            x, y = positions[corner]
            self.window.geometry(f"+{x}+{y}")
            self.save_window_position()
            print(f"窗口移动到: {corner}")

    def cleanup(self):
        """清理资源"""
        self.save_window_position()
        print("窗口管理器已清理")
