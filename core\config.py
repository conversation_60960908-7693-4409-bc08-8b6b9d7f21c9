"""
配置管理模块 - 重新设计版本
支持双模式界面的配置管理
"""
import json
import os
from typing import Dict, Any, Tu<PERSON>
from dataclasses import dataclass, asdict

@dataclass
class ServerConfig:
    """服务器配置"""
    url: str = "http://localhost:8188"
    refresh_interval: int = 1000  # 毫秒
    timeout: int = 5  # 秒
    retry_interval: int = 3000  # 毫秒

@dataclass
class UIConfig:
    """界面配置"""
    mode: str = "mini"  # mini 或 detailed
    always_on_top: bool = True
    transparency: float = 0.9
    theme: str = "darkly"
    font_family: str = "Microsoft YaHei UI"
    font_size: int = 9

@dataclass
class WidgetConfig:
    """小挂件配置"""
    mini_size: Tuple[int, int] = (120, 80)
    detailed_size: Tuple[int, int] = (300, 200)
    position: Tuple[int, int] = (100, 100)
    auto_hide: bool = False
    snap_to_edge: bool = True
    snap_distance: int = 15

@dataclass
class NotificationConfig:
    """通知配置"""
    enabled: bool = True
    flash_on_complete: bool = True
    flash_count: int = 3
    sound: bool = False
    show_popup: bool = True

@dataclass
class DisplayConfig:
    """显示配置"""
    show_task_id: bool = True
    show_queue_info: bool = True
    show_queue_details: bool = True
    show_execution_time: bool = True
    show_node_details: bool = True
    show_progress_percentage: bool = True

class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: str = "widget_config.json"):
        self.config_file = config_file

        # 初始化配置对象
        self.server = ServerConfig()
        self.ui = UIConfig()
        self.widget = WidgetConfig()
        self.notification = NotificationConfig()
        self.display = DisplayConfig()

        # 加载配置
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 更新配置对象
                if 'server' in data:
                    self._update_dataclass(self.server, data['server'])
                if 'ui' in data:
                    self._update_dataclass(self.ui, data['ui'])
                if 'widget' in data:
                    self._update_dataclass(self.widget, data['widget'])
                if 'notification' in data:
                    self._update_dataclass(self.notification, data['notification'])
                if 'display' in data:
                    self._update_dataclass(self.display, data['display'])

                print("配置文件加载成功")

            except (json.JSONDecodeError, IOError) as e:
                print(f"配置文件加载失败: {e}")
                print("使用默认配置")
        else:
            print("配置文件不存在，使用默认配置")

    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            config_data = {
                'server': asdict(self.server),
                'ui': asdict(self.ui),
                'widget': asdict(self.widget),
                'notification': asdict(self.notification),
                'display': asdict(self.display)
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)

            print("配置文件保存成功")
            return True

        except IOError as e:
            print(f"配置文件保存失败: {e}")
            return False

    def _update_dataclass(self, obj, data: Dict[str, Any]):
        """更新数据类对象"""
        for key, value in data.items():
            if hasattr(obj, key):
                # 处理元组类型的特殊情况
                if key in ['mini_size', 'detailed_size', 'position'] and isinstance(value, list):
                    value = tuple(value)
                setattr(obj, key, value)

    def get_window_geometry(self, mode: str = "mini") -> str:
        """获取窗口几何信息字符串"""
        if mode == "mini":
            width, height = self.widget.mini_size
        else:
            width, height = self.widget.detailed_size

        x, y = self.widget.position
        return f"{width}x{height}+{x}+{y}"

    def set_window_position(self, x: int, y: int):
        """设置窗口位置"""
        self.widget.position = (x, y)

    def set_window_size(self, width: int, height: int, mode: str = "mini"):
        """设置窗口大小"""
        if mode == "mini":
            self.widget.mini_size = (width, height)
        else:
            self.widget.detailed_size = (width, height)

    def toggle_mode(self):
        """切换界面模式"""
        self.ui.mode = "detailed" if self.ui.mode == "mini" else "mini"

    def is_mini_mode(self) -> bool:
        """检查是否为小挂件模式"""
        return self.ui.mode == "mini"

    def reset_to_default(self):
        """重置为默认配置"""
        self.server = ServerConfig()
        self.ui = UIConfig()
        self.widget = WidgetConfig()
        self.notification = NotificationConfig()
        self.display = DisplayConfig()

    def export_config(self) -> Dict[str, Any]:
        """导出配置为字典"""
        return {
            'server': asdict(self.server),
            'ui': asdict(self.ui),
            'widget': asdict(self.widget),
            'notification': asdict(self.notification),
            'display': asdict(self.display)
        }

    def import_config(self, config_data: Dict[str, Any]):
        """从字典导入配置"""
        if 'server' in config_data:
            self._update_dataclass(self.server, config_data['server'])
        if 'ui' in config_data:
            self._update_dataclass(self.ui, config_data['ui'])
        if 'widget' in config_data:
            self._update_dataclass(self.widget, config_data['widget'])
        if 'notification' in config_data:
            self._update_dataclass(self.notification, config_data['notification'])
        if 'display' in config_data:
            self._update_dataclass(self.display, config_data['display'])

# 全局配置实例
config = ConfigManager()
