"""
API 测试脚本
用于测试 ComfyUI TaskMonitor API 连接和数据解析
"""
import sys
import json
from api_client import ComfyUIApiClient, TaskStatus

def test_api_connection(server_url="http://localhost:8188"):
    """测试 API 连接"""
    print(f"测试连接到: {server_url}")
    print("-" * 50)
    
    # 创建客户端
    client = ComfyUIApiClient(server_url, timeout=5)
    
    try:
        # 获取状态
        data = client.get_status()
        
        print(f"连接状态: {client.get_connection_status().value}")
        print(f"任务状态: {data.status.value}")
        print(f"任务 ID: {data.task_id}")
        
        # 显示队列信息
        print(f"\n队列信息:")
        print(f"  运行中: {data.queue.running_count}")
        print(f"  等待中: {data.queue.pending_count}")
        
        # 显示工作流进度
        print(f"\n工作流进度:")
        if data.workflow_progress.total_nodes:
            print(f"  总节点数: {data.workflow_progress.total_nodes}")
            print(f"  已执行: {data.workflow_progress.executed_nodes}")
            print(f"  最后执行节点: {data.workflow_progress.last_executed_node_id}")
        else:
            print("  无工作流数据")
        
        # 显示当前任务进度
        print(f"\n当前任务进度:")
        if data.current_task_progress.node_type:
            print(f"  节点类型: {data.current_task_progress.node_type}")
            print(f"  节点 ID: {data.current_task_progress.node_id}")
            print(f"  步骤: {data.current_task_progress.step}/{data.current_task_progress.total_steps}")
            print(f"  消息: {data.current_task_progress.text_message}")
        else:
            print("  无任务进度数据")
        
        # 显示执行时间
        if data.execution_time:
            print(f"\n执行时间: {data.execution_time:.2f} 秒")
        
        # 显示错误信息
        if data.error_info:
            print(f"\n错误信息:")
            for error in data.error_info:
                print(f"  - {error}")
        
        print("\n✅ API 测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ API 测试失败: {str(e)}")
        return False

def test_config():
    """测试配置管理"""
    print("\n测试配置管理")
    print("-" * 50)
    
    try:
        from config import Config
        
        # 创建配置实例
        config = Config("test_config.json")
        
        # 测试设置和获取
        config.set('test_key', 'test_value')
        value = config.get('test_key')
        print(f"设置测试: {value}")
        
        # 测试保存和加载
        config.save_config()
        print("✅ 配置保存成功")
        
        # 测试默认值
        default_value = config.get('non_existent_key', 'default')
        print(f"默认值测试: {default_value}")
        
        print("✅ 配置管理测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("ComfyUI TaskMonitor 桌面小部件 - API 测试")
    print("=" * 60)
    
    # 检查命令行参数
    server_url = "http://localhost:8188"
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    # 测试 API 连接
    api_success = test_api_connection(server_url)
    
    # 测试配置管理
    config_success = test_config()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"API 连接: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"配置管理: {'✅ 成功' if config_success else '❌ 失败'}")
    
    if api_success and config_success:
        print("\n🎉 所有测试通过！应用程序应该可以正常运行。")
    else:
        print("\n⚠️  部分测试失败，请检查相关配置。")
        if not api_success:
            print("   - 确保 ComfyUI 服务器正在运行")
            print("   - 确保已安装 ComfyUI-TaskMonitor 扩展")
            print("   - 检查服务器地址是否正确")

if __name__ == "__main__":
    main()
