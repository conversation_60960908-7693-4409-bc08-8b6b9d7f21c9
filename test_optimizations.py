"""
测试优化功能
验证透明背景、动画控制、双击展开等功能
"""
import sys
import os
import tkinter as tk

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_transparent_background():
    """测试透明背景功能"""
    print("测试透明背景功能...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("透明背景测试")
        root.geometry("120x80")
        root.overrideredirect(True)
        
        # 设置透明背景
        root.configure(bg='black')
        root.attributes('-transparentcolor', 'black')
        root.attributes('-topmost', True)
        
        # 创建测试标签
        label = tk.Label(
            root,
            text="🤖",
            font=("Arial", 24),
            fg="white",
            bg="black"
        )
        label.pack(expand=True)
        
        print("✅ 透明背景窗口创建成功")
        print("应该看到一个只显示机器人图标的透明窗口")
        
        # 显示3秒后关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 透明背景测试失败: {e}")
        return False

def test_pet_animation_control():
    """测试宠物动画控制"""
    print("\n测试宠物动画控制...")
    
    try:
        from ui.pet_animation import PetAnimation
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("动画控制测试")
        root.geometry("120x120")
        root.configure(bg='black')
        
        # 创建宠物动画
        pet = PetAnimation(root, "meizi")
        
        print(f"✅ 宠物动画创建成功，加载了 {len(pet.animation_frames)} 帧")
        
        # 测试动画控制
        def test_animation_sequence():
            print("显示第一帧静态图...")
            pet.show_first_frame()
            
            root.after(2000, lambda: (
                print("开始播放动画..."),
                pet.start_animation()
            ))
            
            root.after(4000, lambda: (
                print("停止动画并回到第一帧..."),
                pet.stop_animation(),
                pet.show_first_frame()
            ))
            
            root.after(6000, root.destroy)
        
        test_animation_sequence()
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 宠物动画控制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_double_click_detection():
    """测试双击检测"""
    print("\n测试双击检测...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("双击检测测试")
        root.geometry("200x100")
        
        click_count = [0]  # 使用列表以便在内部函数中修改
        
        def on_double_click(event):
            click_count[0] += 1
            print(f"检测到双击事件 #{click_count[0]}")
        
        # 创建测试标签
        label = tk.Label(
            root,
            text="双击我测试",
            font=("Microsoft YaHei UI", 12),
            bg="lightblue"
        )
        label.pack(expand=True, fill="both")
        
        # 绑定双击事件
        label.bind("<Double-Button-1>", on_double_click)
        root.bind("<Double-Button-1>", on_double_click)
        
        print("✅ 双击检测设置成功")
        print("请双击窗口测试双击检测...")
        
        # 5秒后自动关闭
        root.after(5000, root.destroy)
        root.mainloop()
        
        if click_count[0] > 0:
            print(f"✅ 双击检测成功，共检测到 {click_count[0]} 次双击")
            return True
        else:
            print("⚠️ 未检测到双击事件")
            return False
        
    except Exception as e:
        print(f"❌ 双击检测测试失败: {e}")
        return False

def test_detail_window():
    """测试详细信息窗口"""
    print("\n测试详细信息窗口...")
    
    try:
        from ui.detail_window import DetailWindow
        from core.config import config
        
        # 创建主窗口
        root = tk.Tk()
        root.title("详细窗口测试")
        root.geometry("120x80+100+100")
        
        # 创建详细信息窗口
        detail_window = DetailWindow(root, config)
        
        # 创建测试数据
        test_data = {
            'status': 'running',
            'task_id': 'test_task_123456789',
            'execution_time': 125.7,
            'workflow_progress': {
                'total_nodes': 25,
                'executed_nodes': 18,
                'last_executed_node_id': 42
            },
            'current_task_progress': {
                'node_id': 42,
                'node_type': 'KSampler',
                'step': 15,
                'total_steps': 20,
                'text_message': '正在生成图像...'
            },
            'queue': {
                'running_count': 1,
                'pending_count': 2,
                'running': [{'prompt_id': 'run123', 'nodes_in_prompt': 15}],
                'pending': [{'prompt_id': 'pend456', 'nodes_in_prompt': 8}]
            },
            'error_info': []
        }
        
        # 更新数据
        detail_window.update_data(test_data)
        
        # 创建控制按钮
        btn_frame = tk.Frame(root)
        btn_frame.pack(pady=10)
        
        tk.Button(
            btn_frame,
            text="显示详细窗口",
            command=detail_window.show
        ).pack(side="left", padx=5)
        
        tk.Button(
            btn_frame,
            text="隐藏详细窗口",
            command=detail_window.hide
        ).pack(side="left", padx=5)
        
        tk.Button(
            btn_frame,
            text="切换显示",
            command=detail_window.toggle_visibility
        ).pack(side="left", padx=5)
        
        print("✅ 详细信息窗口创建成功")
        print("请点击按钮测试详细窗口的显示/隐藏...")
        
        # 10秒后自动关闭
        root.after(10000, lambda: (detail_window.cleanup(), root.destroy()))
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 详细信息窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("ComfyUI TaskMonitor 优化功能测试")
    print("=" * 60)
    
    tests = [
        ("透明背景功能", test_transparent_background),
        ("宠物动画控制", test_pet_animation_control),
        ("双击检测功能", test_double_click_detection),
        ("详细信息窗口", test_detail_window),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n[{name}]")
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 通过")
            else:
                print(f"❌ {name} 失败")
        except Exception as e:
            print(f"❌ {name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有优化功能测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序异常退出: {e}")
        input("按回车键退出...")
