ComfyUI TaskMonitor 桌面小部件 - 使用说明

=== 快速启动 ===

方法1（推荐）：
双击运行 run.py

方法2：
在命令行中运行：
python run.py

方法3：
直接运行主程序：
python main.py

=== 首次使用 ===

1. 确保已安装 Python 3.7 或更高版本
2. 确保 ComfyUI 服务器正在运行
3. 确保已安装 ComfyUI-TaskMonitor 扩展
4. 运行应用程序后，点击设置按钮配置服务器地址

=== 功能说明 ===

- 实时监控 ComfyUI 工作流程进度
- 显示任务状态、队列信息、执行时间
- 无边框窗口，支持拖动和边缘吸附
- 可调节透明度和置顶显示
- 工作流完成时的通知提醒
- 丰富的自定义设置选项

=== 设置选项 ===

服务器设置：
- 服务器地址（默认：http://localhost:8188）
- 刷新间隔、连接超时等

窗口设置：
- 窗口大小、位置
- 置顶显示、透明度
- 边缘吸附功能

外观设置：
- 主题选择
- 字体和颜色
- 紧凑模式

通知设置：
- 桌面通知
- 完成提醒
- 显示选项

=== 故障排除 ===

如果遇到问题：

1. 检查 Python 版本：python --version
2. 安装依赖：pip install -r requirements.txt
3. 检查 ComfyUI 服务器是否运行
4. 检查 ComfyUI-TaskMonitor 扩展是否安装
5. 查看控制台错误信息

=== 联系支持 ===

如有问题，请查看 README.md 文档或提交 Issue。
