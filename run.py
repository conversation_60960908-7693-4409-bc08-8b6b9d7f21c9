"""
运行脚本
简单的启动器，用于启动 ComfyUI TaskMonitor 桌面小部件
"""
import os
import sys
import subprocess

def main():
    """主函数"""
    print("Starting ComfyUI TaskMonitor Desktop Widget...")
    
    # 检查依赖是否已安装
    try:
        import ttkbootstrap
        import requests
        print("✅ All dependencies are installed")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Installing dependencies...")
        
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run: pip install -r requirements.txt")
            input("Press Enter to exit...")
            return
    
    # 启动应用程序
    try:
        from main import main as app_main
        app_main()
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
