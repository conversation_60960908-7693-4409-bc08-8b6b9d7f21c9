"""
ComfyUI TaskMonitor 桌面小挂件 - 重新设计版本
主应用程序入口
"""
import sys
import os
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.config import config
from core.state_manager import state_manager, StateChange, TaskState
from api_client import ComfyUIApiClient, TaskMonitorData, TaskStatus
from ui.widget_mini import MiniWidget
from utils.window_manager import WindowManager

class TaskMonitorApp:
    """ComfyUI TaskMonitor 桌面小挂件应用程序"""
    
    def __init__(self):
        print("初始化 ComfyUI TaskMonitor 桌面小挂件...")
        
        # 初始化 API 客户端
        self.api_client = ComfyUIApiClient(
            base_url=config.server.url,
            timeout=config.server.timeout
        )
        
        # 应用程序状态
        self.running = True
        self.update_thread = None
        
        # 创建主窗口
        self.setup_window()
        
        # 创建界面
        self.setup_ui()
        
        # 设置窗口管理器
        self.setup_window_manager()
        
        # 设置状态监听器
        self.setup_state_listeners()
        
        # 启动数据更新线程
        self.start_update_thread()
        
        print("应用程序初始化完成")
    
    def setup_window(self):
        """设置主窗口"""
        self.root = ttk_bs.Window(
            title="ComfyUI TaskMonitor",
            themename=config.ui.theme,
            size=config.widget.mini_size,
            resizable=(False, False)
        )
        
        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置应用程序图标
            pass
        except:
            pass
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建小挂件界面
        self.mini_widget = MiniWidget(
            parent=self.root,
            on_click=self.on_widget_click
        )
        
        print("小挂件界面创建完成")
    
    def setup_window_manager(self):
        """设置窗口管理器"""
        self.window_manager = WindowManager(self.root, config)
        
        # 设置无边框窗口
        self.window_manager.setup_borderless_window()
        
        # 设置拖动事件
        draggable_widgets = self.mini_widget.get_widgets_for_drag()
        self.window_manager.setup_drag_events(draggable_widgets)
        
        print("窗口管理器设置完成")
    
    def setup_state_listeners(self):
        """设置状态监听器"""
        state_manager.add_state_listener(self.on_state_change)
        print("状态监听器设置完成")
    
    def on_widget_click(self):
        """处理小挂件点击事件"""
        print("小挂件被点击，准备展开详细界面...")
        # TODO: 实现详细界面展开
        self.show_detailed_view()
    
    def show_detailed_view(self):
        """显示详细视图"""
        # 暂时显示一个消息框
        import tkinter.messagebox as msgbox
        msgbox.showinfo("详细视图", "详细视图功能正在开发中...\n\n当前状态信息:\n" + 
                       f"应用状态: {state_manager.app_state.value}\n" +
                       f"任务状态: {state_manager.task_state.value}\n" +
                       f"执行时间: {state_manager.get_current_execution_time():.1f} 秒")
    
    def on_state_change(self, change: StateChange):
        """处理状态变化"""
        print(f"状态变化: {change.old_state.value} -> {change.new_state.value}")
        
        # 处理完成通知
        if (change.old_state == TaskState.RUNNING and 
            change.new_state == TaskState.COMPLETED and 
            config.notification.flash_on_complete):
            self.show_completion_notification()
    
    def show_completion_notification(self):
        """显示完成通知"""
        print("工作流完成，开始闪烁提醒")
        
        # 开始闪烁动画
        self.mini_widget.start_flash_animation(config.notification.flash_count)
        
        # 如果启用了弹窗通知
        if config.notification.show_popup:
            self.root.after(1000, self.show_completion_popup)
    
    def show_completion_popup(self):
        """显示完成弹窗"""
        import tkinter.messagebox as msgbox
        msgbox.showinfo("任务完成", "🎉 ComfyUI 工作流执行完成！")
    
    def start_update_thread(self):
        """启动数据更新线程"""
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()
        print("数据更新线程已启动")
    
    def update_loop(self):
        """数据更新循环"""
        while self.running:
            try:
                # 检查是否应该重试连接
                if not state_manager.is_connected() and state_manager.should_retry_connection():
                    state_manager.record_connection_attempt()
                
                # 获取数据
                data = self.api_client.get_status()
                
                # 更新状态管理器
                self.update_state_from_data(data)
                
                # 在主线程中更新 UI
                self.root.after(0, self.update_ui, data)
                
                # 等待下次更新
                interval = config.server.refresh_interval / 1000.0
                time.sleep(interval)
                
            except Exception as e:
                print(f"更新循环错误: {e}")
                time.sleep(1)
    
    def update_state_from_data(self, data: TaskMonitorData):
        """从数据更新状态管理器"""
        # 映射 API 状态到内部状态
        status_mapping = {
            TaskStatus.IDLE: TaskState.IDLE,
            TaskStatus.RUNNING: TaskState.RUNNING,
            TaskStatus.COMPLETED: TaskState.COMPLETED,
            TaskStatus.ERROR: TaskState.ERROR,
            TaskStatus.QUEUED: TaskState.QUEUED,
            TaskStatus.CONNECTING: TaskState.IDLE,  # 连接中视为空闲
            TaskStatus.DISCONNECTED: TaskState.IDLE
        }
        
        # 更新任务状态
        if data.status in status_mapping:
            state_manager.set_task_state(status_mapping[data.status])
        
        # 更新应用状态
        if data.status == TaskStatus.DISCONNECTED:
            from core.state_manager import AppState
            state_manager.set_app_state(AppState.DISCONNECTED)
        elif data.status == TaskStatus.CONNECTING:
            from core.state_manager import AppState
            state_manager.set_app_state(AppState.CONNECTING)
        else:
            from core.state_manager import AppState
            state_manager.set_app_state(AppState.CONNECTED)
            state_manager.reset_connection_attempts()
    
    def update_ui(self, data: TaskMonitorData):
        """更新用户界面"""
        try:
            # 准备显示数据
            display_data = {
                'status': data.status.value,
                'task_id': data.task_id,
                'queue': {
                    'running_count': data.queue.running_count,
                    'pending_count': data.queue.pending_count,
                    'running': data.queue.running,
                    'pending': data.queue.pending
                },
                'current_task_progress': {
                    'node_id': data.current_task_progress.node_id,
                    'node_type': data.current_task_progress.node_type,
                    'step': data.current_task_progress.step,
                    'total_steps': data.current_task_progress.total_steps,
                    'text_message': data.current_task_progress.text_message
                },
                'workflow_progress': {
                    'total_nodes': data.workflow_progress.total_nodes,
                    'executed_nodes': data.workflow_progress.executed_nodes,
                    'last_executed_node_id': data.workflow_progress.last_executed_node_id
                },
                'execution_time': data.execution_time or state_manager.get_current_execution_time(),
                'error_info': data.error_info
            }
            
            # 更新小挂件显示
            self.mini_widget.update_display(display_data)
            
        except Exception as e:
            print(f"UI 更新错误: {e}")
            import traceback
            traceback.print_exc()
    
    def create_context_menu(self):
        """创建右键菜单"""
        menu = tk.Menu(self.root, tearoff=0)
        
        menu.add_command(label="显示详细信息", command=self.show_detailed_view)
        menu.add_separator()
        menu.add_checkbutton(
            label="始终置顶",
            variable=tk.BooleanVar(value=config.ui.always_on_top),
            command=self.window_manager.toggle_always_on_top
        )
        menu.add_separator()
        menu.add_command(label="设置", command=self.show_settings)
        menu.add_command(label="退出", command=self.close_application)
        
        return menu
    
    def show_settings(self):
        """显示设置窗口"""
        # TODO: 实现设置窗口
        import tkinter.messagebox as msgbox
        msgbox.showinfo("设置", "设置功能正在开发中...")
    
    def close_application(self):
        """关闭应用程序"""
        print("正在关闭应用程序...")
        
        self.running = False
        
        # 保存配置
        config.save_config()
        
        # 清理资源
        self.cleanup()
        
        # 关闭窗口
        self.root.quit()
        self.root.destroy()
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'mini_widget'):
                self.mini_widget.cleanup()
            
            if hasattr(self, 'window_manager'):
                self.window_manager.cleanup()
            
            state_manager.cleanup()
            
            print("资源清理完成")
        except Exception as e:
            print(f"清理资源时出错: {e}")
    
    def run(self):
        """运行应用程序"""
        try:
            # 设置关闭事件处理
            self.root.protocol("WM_DELETE_WINDOW", self.close_application)
            
            # 绑定右键菜单
            context_menu = self.create_context_menu()
            
            def show_context_menu(event):
                try:
                    context_menu.tk_popup(event.x_root, event.y_root)
                finally:
                    context_menu.grab_release()
            
            self.root.bind("<Button-3>", show_context_menu)
            
            print("应用程序开始运行...")
            
            # 启动主循环
            self.root.mainloop()
            
        except KeyboardInterrupt:
            print("用户中断程序")
            self.close_application()
        except Exception as e:
            print(f"应用程序运行时出错: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    try:
        print("=" * 60)
        print("ComfyUI TaskMonitor 桌面小挂件 v2.0")
        print("=" * 60)
        
        app = TaskMonitorApp()
        app.run()
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
