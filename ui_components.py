"""
UI 组件模块
定义应用程序的界面组件
"""
import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from api_client import TaskMonitorData, TaskStatus
from typing import Optional, Callable

class StatusIndicator(ttk_bs.Frame):
    """状态指示器组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.status_colors = {
            TaskStatus.IDLE: "secondary",
            TaskStatus.RUNNING: "primary",
            TaskStatus.COMPLETED: "success",
            TaskStatus.ERROR: "danger",
            TaskStatus.QUEUED: "warning",
            TaskStatus.CONNECTING: "info",
            TaskStatus.DISCONNECTED: "dark"
        }

        self.status_texts = {
            TaskStatus.IDLE: "空闲",
            TaskStatus.RUNNING: "运行中",
            TaskStatus.COMPLETED: "已完成",
            TaskStatus.ERROR: "错误",
            TaskStatus.QUEUED: "排队中",
            TaskStatus.CONNECTING: "连接中...",
            TaskStatus.DISCONNECTED: "未连接"
        }

        # 创建状态标签
        self.status_label = ttk_bs.Label(
            self,
            text="未连接",
            bootstyle="dark",
            font=("Microsoft YaHei UI", 10, "bold")
        )
        self.status_label.pack(pady=2)

        # 创建状态指示点
        self.indicator = ttk_bs.Label(
            self,
            text="●",
            bootstyle="dark",
            font=("Arial", 12)
        )
        self.indicator.pack()

    def update_status(self, status: TaskStatus):
        """更新状态显示"""
        color = self.status_colors.get(status, "secondary")
        text = self.status_texts.get(status, "未知")

        self.status_label.config(text=text, bootstyle=color)
        self.indicator.config(bootstyle=color)

class TaskIdDisplay(ttk_bs.Frame):
    """任务ID显示组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.task_id_label = ttk_bs.Label(
            self,
            text="任务ID:",
            font=("Microsoft YaHei UI", 9)
        )
        self.task_id_label.pack(anchor="w", pady=(5, 2))

        self.task_id_value = ttk_bs.Label(
            self,
            text="无",
            font=("Microsoft YaHei UI", 8),
            bootstyle="secondary"
        )
        self.task_id_value.pack(anchor="w")

    def update_task_id(self, task_id: Optional[str]):
        """更新任务ID"""
        if task_id:
            # 显示前12位，避免过长
            display_id = task_id[:12] + "..." if len(task_id) > 12 else task_id
            self.task_id_value.config(text=display_id)
        else:
            self.task_id_value.config(text="无")

class ProgressDisplay(ttk_bs.Frame):
    """进度显示组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # 工作流进度
        self.workflow_label = ttk_bs.Label(
            self,
            text="工作流进度:",
            font=("Microsoft YaHei UI", 9)
        )
        self.workflow_label.pack(anchor="w", pady=(5, 2))

        self.workflow_progress = ttk_bs.Progressbar(
            self,
            bootstyle="primary-striped",
            mode="determinate"
        )
        self.workflow_progress.pack(fill="x", pady=(0, 5))

        self.workflow_text = ttk_bs.Label(
            self,
            text="0 / 0 节点",
            font=("Microsoft YaHei UI", 8),
            bootstyle="secondary"
        )
        self.workflow_text.pack(anchor="w")

        # 当前任务进度
        self.task_label = ttk_bs.Label(
            self,
            text="当前任务:",
            font=("Microsoft YaHei UI", 9)
        )
        self.task_label.pack(anchor="w", pady=(10, 2))

        self.task_progress = ttk_bs.Progressbar(
            self,
            bootstyle="success-striped",
            mode="determinate"
        )
        self.task_progress.pack(fill="x", pady=(0, 5))

        self.task_text = ttk_bs.Label(
            self,
            text="等待任务...",
            font=("Microsoft YaHei UI", 8),
            bootstyle="secondary"
        )
        self.task_text.pack(anchor="w")

        # 节点信息
        self.node_info = ttk_bs.Label(
            self,
            text="",
            font=("Microsoft YaHei UI", 8),
            bootstyle="info",
            wraplength=350
        )
        self.node_info.pack(anchor="w", pady=(5, 0))

    def update_progress(self, data: TaskMonitorData):
        """更新进度显示"""
        # 更新工作流进度
        if data.workflow_progress.total_nodes and data.workflow_progress.executed_nodes is not None:
            total = data.workflow_progress.total_nodes
            executed = data.workflow_progress.executed_nodes
            progress = (executed / total * 100) if total > 0 else 0

            self.workflow_progress.config(value=progress)
            self.workflow_text.config(text=f"{executed} / {total} 节点")
        else:
            self.workflow_progress.config(value=0)
            self.workflow_text.config(text="0 / 0 节点")

        # 更新当前任务进度
        if data.current_task_progress.total_steps and data.current_task_progress.step is not None:
            total = data.current_task_progress.total_steps
            current = data.current_task_progress.step
            progress = (current / total * 100) if total > 0 else 0

            self.task_progress.config(value=progress)
            self.task_text.config(text=f"{current} / {total} 步骤")
        else:
            self.task_progress.config(value=0)
            if data.status == TaskStatus.RUNNING:
                self.task_text.config(text="执行中...")
            else:
                self.task_text.config(text="等待任务...")

        # 更新节点信息
        node_info = ""
        if data.current_task_progress.node_type:
            node_info += f"节点类型: {data.current_task_progress.node_type}"
        if data.current_task_progress.text_message:
            if node_info:
                node_info += "\n"
            node_info += f"消息: {data.current_task_progress.text_message}"

        self.node_info.config(text=node_info)

class QueueDisplay(ttk_bs.Frame):
    """队列显示组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.queue_label = ttk_bs.Label(
            self,
            text="队列信息:",
            font=("Microsoft YaHei UI", 9)
        )
        self.queue_label.pack(anchor="w", pady=(5, 2))

        self.queue_summary = ttk_bs.Label(
            self,
            text="运行中: 0, 等待中: 0",
            font=("Microsoft YaHei UI", 8),
            bootstyle="secondary"
        )
        self.queue_summary.pack(anchor="w")

        # 详细队列信息（可选显示）
        self.queue_details = ttk_bs.Label(
            self,
            text="",
            font=("Microsoft YaHei UI", 7),
            bootstyle="info",
            wraplength=350
        )
        # 默认不显示，根据配置决定

    def update_queue(self, data: TaskMonitorData):
        """更新队列信息"""
        running = data.queue.running_count
        pending = data.queue.pending_count
        self.queue_summary.config(text=f"运行中: {running}, 等待中: {pending}")

        # 显示详细队列信息
        details = []
        if data.queue.running:
            for i, task in enumerate(data.queue.running[:3]):  # 最多显示3个
                prompt_id = task.get('prompt_id', 'N/A')[:8]  # 截取前8位
                nodes = task.get('nodes_in_prompt', 'N/A')
                client_id = task.get('client_id', 'N/A')[:8]  # 截取前8位
                details.append(f"运行中 {i+1}: ID:{prompt_id} 节点:{nodes} 客户端:{client_id}")

        if data.queue.pending:
            for i, task in enumerate(data.queue.pending[:2]):  # 最多显示2个
                prompt_id = task.get('prompt_id', 'N/A')[:8]
                nodes = task.get('nodes_in_prompt', 'N/A')
                details.append(f"等待中 {i+1}: ID:{prompt_id} 节点:{nodes}")

        self.queue_details.config(text="\n".join(details))

    def set_show_details(self, show_details: bool):
        """设置是否显示队列详情"""
        if show_details:
            self.queue_details.pack(anchor="w", pady=(2, 0))
        else:
            self.queue_details.pack_forget()

class ExecutionTimeDisplay(ttk_bs.Frame):
    """执行时间显示组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.time_label = ttk_bs.Label(
            self,
            text="执行时间:",
            font=("Microsoft YaHei UI", 9)
        )
        self.time_label.pack(anchor="w", pady=(5, 2))

        self.time_value = ttk_bs.Label(
            self,
            text="0.0 秒",
            font=("Microsoft YaHei UI", 8),
            bootstyle="secondary"
        )
        self.time_value.pack(anchor="w")

    def update_time(self, execution_time: Optional[float]):
        """更新执行时间"""
        if execution_time is not None:
            self.time_value.config(text=f"{execution_time:.1f} 秒")
        else:
            self.time_value.config(text="0.0 秒")

class ErrorDisplay(ttk_bs.Frame):
    """错误信息显示组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.error_label = ttk_bs.Label(
            self,
            text="",
            font=("Microsoft YaHei UI", 8),
            bootstyle="danger",
            wraplength=350
        )
        self.error_label.pack(anchor="w", pady=2)

    def update_errors(self, errors: list):
        """更新错误信息"""
        if errors:
            error_text = "\n".join(errors)
            self.error_label.config(text=f"错误: {error_text}")
            self.pack(fill="x", pady=5)
        else:
            self.error_label.config(text="")
            self.pack_forget()

class ControlPanel(ttk_bs.Frame):
    """控制面板组件"""

    def __init__(self, parent, on_settings: Callable = None, on_close: Callable = None, **kwargs):
        super().__init__(parent, **kwargs)

        # 创建按钮框架
        button_frame = ttk_bs.Frame(self)
        button_frame.pack(fill="x", pady=5)

        # 设置按钮
        if on_settings:
            self.settings_btn = ttk_bs.Button(
                button_frame,
                text="⚙",
                bootstyle="secondary-outline",
                width=3,
                command=on_settings
            )
            self.settings_btn.pack(side="left", padx=(0, 5))

        # 关闭按钮
        if on_close:
            self.close_btn = ttk_bs.Button(
                button_frame,
                text="✕",
                bootstyle="danger-outline",
                width=3,
                command=on_close
            )
            self.close_btn.pack(side="right")
