"""
快速启动脚本
用于在没有安装依赖的情况下测试基本功能
"""
import sys
import os

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        missing_packages.append("tkinter")
        print("❌ tkinter 不可用")
    
    try:
        import ttkbootstrap
        print("✅ ttkbootstrap 可用")
    except ImportError:
        missing_packages.append("ttkbootstrap")
        print("❌ ttkbootstrap 不可用")
    
    try:
        import requests
        print("✅ requests 可用")
    except ImportError:
        missing_packages.append("requests")
        print("❌ requests 不可用")
    
    try:
        import plyer
        print("✅ plyer 可用")
    except ImportError:
        missing_packages.append("plyer")
        print("❌ plyer 不可用（可选）")
    
    return missing_packages

def install_missing_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
    
    print(f"\n需要安装以下包: {', '.join(packages)}")
    
    try:
        import subprocess
        for package in packages:
            if package == "tkinter":
                print("tkinter 是 Python 标准库的一部分，请检查 Python 安装")
                continue
            
            print(f"正在安装 {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败: {result.stderr}")
                return False
        
        return True
    except Exception as e:
        print(f"安装过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("ComfyUI TaskMonitor 桌面小部件 - 快速启动")
    print("=" * 50)
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 需要 Python 3.7 或更高版本")
        return False
    
    print("✅ Python 版本符合要求")
    print()
    
    # 检查依赖
    print("检查依赖包...")
    missing = check_dependencies()
    
    if missing:
        print(f"\n发现 {len(missing)} 个缺失的包")
        
        choice = input("是否自动安装缺失的包？(y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            if install_missing_packages(missing):
                print("\n✅ 所有依赖包安装完成")
            else:
                print("\n❌ 依赖包安装失败")
                print("请手动运行: pip install -r requirements.txt")
                return False
        else:
            print("请手动安装依赖包后再运行应用程序")
            return False
    else:
        print("\n✅ 所有依赖包都已安装")
    
    # 启动应用程序
    print("\n启动应用程序...")
    try:
        from main import main as app_main
        app_main()
        return True
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序异常退出: {e}")
        input("按回车键退出...")
