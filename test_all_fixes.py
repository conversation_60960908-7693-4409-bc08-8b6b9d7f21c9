"""
测试所有修复的功能
验证拖动、边缘吸附和信息显示
"""
import tkinter as tk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from api_client import TaskMonitorData, TaskStatus, QueueInfo, TaskProgress, WorkflowProgress
from ui_components import (
    StatusIndicator, ProgressDisplay, QueueDisplay, 
    ExecutionTimeDisplay, ErrorDisplay, TaskIdDisplay
)

class AllFixesTest:
    def __init__(self):
        # 创建窗口
        self.root = ttk_bs.Window(
            title="所有修复测试",
            themename="darkly",
            size=(420, 500),  # 使用新的默认大小
            resizable=(True, True)
        )
        
        # 设置无边框
        self.root.overrideredirect(True)
        self.root.attributes('-topmost', True)
        self.root.attributes('-alpha', 0.9)
        
        # 拖动相关变量
        self.dragging = False
        self.start_x = 0
        self.start_y = 0
        
        # 创建测试数据
        self.create_test_data()
        
        # 创建UI
        self.setup_ui()
        
        # 设置拖动功能
        self.setup_drag_functionality()
        
        # 开始测试
        self.start_test()
    
    def create_test_data(self):
        """创建完整的测试数据"""
        self.test_data = TaskMonitorData()
        self.test_data.task_id = "test_task_abcdef123456"
        self.test_data.status = TaskStatus.RUNNING
        
        # 队列信息
        self.test_data.queue = QueueInfo(
            running_count=2,
            pending_count=3,
            running=[
                {"prompt_id": "run_prompt_123", "nodes_in_prompt": 15, "client_id": "client_abc"},
                {"prompt_id": "run_prompt_456", "nodes_in_prompt": 8, "client_id": "client_def"}
            ],
            pending=[
                {"prompt_id": "pend_prompt_111", "nodes_in_prompt": 12},
                {"prompt_id": "pend_prompt_222", "nodes_in_prompt": 6},
                {"prompt_id": "pend_prompt_333", "nodes_in_prompt": 20}
            ]
        )
        
        # 当前任务进度
        self.test_data.current_task_progress = TaskProgress(
            node_id=42,
            node_type="KSampler",
            step=15,
            total_steps=20,
            text_message="正在生成图像，步骤 15/20"
        )
        
        # 工作流进度
        self.test_data.workflow_progress = WorkflowProgress(
            total_nodes=25,
            executed_nodes=18,
            last_executed_node_id=42
        )
        
        # 执行时间
        self.test_data.execution_time = 125.7
        
        # 错误信息
        self.test_data.error_info = []
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_frame = ttk_bs.Frame(self.root, padding=10)
        main_frame.pack(fill="both", expand=True)
        
        # 标题栏
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill="x", pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="所有修复测试",
            font=("Microsoft YaHei UI", 11, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side="left")
        
        # 关闭按钮
        close_btn = ttk_bs.Button(
            title_frame,
            text="✕",
            bootstyle="danger-outline",
            width=3,
            command=self.close_window
        )
        close_btn.pack(side="right")
        
        # 拖动提示
        drag_hint = ttk_bs.Label(
            main_frame,
            text="在此区域拖动窗口（非按钮区域）",
            font=("Microsoft YaHei UI", 8),
            bootstyle="info"
        )
        drag_hint.pack(pady=(0, 5))
        
        # 状态指示器
        self.status_indicator = StatusIndicator(main_frame)
        self.status_indicator.pack(fill="x", pady=(0, 5))
        
        # 任务ID显示
        self.task_id_display = TaskIdDisplay(main_frame)
        self.task_id_display.pack(fill="x", pady=(0, 5))
        
        # 进度显示
        self.progress_display = ProgressDisplay(main_frame)
        self.progress_display.pack(fill="x", pady=(0, 5))
        
        # 队列显示
        self.queue_display = QueueDisplay(main_frame)
        self.queue_display.pack(fill="x", pady=(0, 5))
        self.queue_display.set_show_details(True)  # 显示详情
        
        # 执行时间显示
        self.time_display = ExecutionTimeDisplay(main_frame)
        self.time_display.pack(fill="x", pady=(0, 5))
        
        # 错误信息显示
        self.error_display = ErrorDisplay(main_frame)
        
        # 测试按钮
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))
        
        ttk_bs.Button(
            button_frame,
            text="添加错误",
            bootstyle="warning",
            command=self.add_error
        ).pack(side="left", padx=(0, 5))
        
        ttk_bs.Button(
            button_frame,
            text="清除错误",
            bootstyle="success",
            command=self.clear_error
        ).pack(side="left")
        
        # 保存可拖动组件引用
        self.draggable_widgets = [
            main_frame, title_frame, title_label, drag_hint,
            self.status_indicator, self.task_id_display, 
            self.progress_display, self.queue_display, 
            self.time_display, self.error_display
        ]
    
    def setup_drag_functionality(self):
        """设置拖动功能"""
        # 为主窗口绑定拖动事件
        self.root.bind('<Button-1>', self.start_drag)
        self.root.bind('<B1-Motion>', self.on_drag)
        self.root.bind('<ButtonRelease-1>', self.stop_drag)
        
        # 延迟绑定子控件事件
        self.root.after(100, self.bind_drag_to_widgets)
    
    def bind_drag_to_widgets(self):
        """为可拖动的控件绑定拖动事件"""
        try:
            # 递归绑定所有子控件，但排除按钮
            self._bind_drag_recursive(self.root)
        except Exception as e:
            print(f"绑定拖动事件时出错: {e}")
    
    def _bind_drag_recursive(self, widget):
        """递归绑定拖动事件到所有子控件"""
        try:
            # 获取控件类型
            widget_class = widget.winfo_class()
            
            # 排除不应该响应拖动的控件
            if widget_class not in ['Button', 'Entry', 'Text', 'Listbox', 'Scrollbar', 'Scale', 'Combobox', 'Spinbox']:
                # 绑定拖动事件
                widget.bind('<Button-1>', self.start_drag, add='+')
                widget.bind('<B1-Motion>', self.on_drag, add='+')
                widget.bind('<ButtonRelease-1>', self.stop_drag, add='+')
            
            # 递归处理子控件
            for child in widget.winfo_children():
                self._bind_drag_recursive(child)
                
        except Exception:
            # 忽略已销毁的控件
            pass
    
    def start_drag(self, event):
        """开始拖动"""
        self.dragging = True
        self.start_x = event.x_root
        self.start_y = event.y_root
        print(f"开始拖动: {event.x_root}, {event.y_root}")
    
    def on_drag(self, event):
        """拖动过程中"""
        if not self.dragging:
            return
        
        # 计算新位置
        x = self.root.winfo_x() + (event.x_root - self.start_x)
        y = self.root.winfo_y() + (event.y_root - self.start_y)
        
        # 应用边缘吸附（改进版本）
        x, y = self.apply_edge_snapping(x, y)
        
        # 移动窗口
        self.root.geometry(f"+{x}+{y}")
        
        # 更新起始位置
        self.start_x = event.x_root
        self.start_y = event.y_root
    
    def stop_drag(self, event):
        """停止拖动"""
        self.dragging = False
        print(f"停止拖动: {event.x_root}, {event.y_root}")
    
    def apply_edge_snapping(self, x, y):
        """应用改进的边缘吸附"""
        snap_distance = 15  # 减少吸附距离
        release_distance = 25  # 增加释放距离
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        
        # 获取当前窗口位置
        current_x = self.root.winfo_x()
        current_y = self.root.winfo_y()
        
        # 左边缘吸附
        if x <= snap_distance and current_x != 0:
            x = 0
        elif current_x == 0 and x < release_distance:
            x = 0
        
        # 右边缘吸附
        elif x + window_width >= screen_width - snap_distance and current_x != screen_width - window_width:
            x = screen_width - window_width
        elif current_x == screen_width - window_width and x + window_width > screen_width - release_distance:
            x = screen_width - window_width
        
        # 上边缘吸附
        if y <= snap_distance and current_y != 0:
            y = 0
        elif current_y == 0 and y < release_distance:
            y = 0
        
        # 下边缘吸附
        elif y + window_height >= screen_height - snap_distance and current_y != screen_height - window_height:
            y = screen_height - window_height
        elif current_y == screen_height - window_height and y + window_height > screen_height - release_distance:
            y = screen_height - window_height
        
        return x, y
    
    def start_test(self):
        """开始测试"""
        print("开始所有修复测试...")
        print("1. 拖动测试: 在非按钮区域拖动窗口")
        print("2. 边缘吸附测试: 拖动到屏幕边缘")
        print("3. 信息显示测试: 查看所有信息是否完整显示")
        
        self.update_all_components()
    
    def update_all_components(self):
        """更新所有组件"""
        try:
            # 更新状态指示器
            self.status_indicator.update_status(self.test_data.status)
            
            # 更新任务ID
            self.task_id_display.update_task_id(self.test_data.task_id)
            
            # 更新进度显示
            self.progress_display.update_progress(self.test_data)
            
            # 更新队列信息
            self.queue_display.update_queue(self.test_data)
            
            # 更新执行时间
            self.time_display.update_time(self.test_data.execution_time)
            
            # 更新错误信息
            self.error_display.update_errors(self.test_data.error_info)
            
            print("所有组件更新完成")
            
        except Exception as e:
            print(f"更新组件时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def add_error(self):
        """添加错误信息"""
        self.test_data.error_info = ["测试错误信息", "连接超时"]
        self.update_all_components()
    
    def clear_error(self):
        """清除错误信息"""
        self.test_data.error_info = []
        self.update_all_components()
    
    def close_window(self):
        """关闭窗口"""
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("用户中断程序")

def main():
    """主函数"""
    try:
        app = AllFixesTest()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
